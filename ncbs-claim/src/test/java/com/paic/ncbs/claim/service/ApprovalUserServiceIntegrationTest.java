package com.paic.ncbs.claim.service;

import com.paic.ncbs.claim.common.util.ApprovalUserHelper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import com.paic.ncbs.claim.common.context.WebServletContext;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 审批用户服务集成测试
 * 验证重构后的 EntrustmentService 和 InvestigateTaskService 功能
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApprovalUserServiceIntegrationTest {

    @Autowired
    private EntrustmentService entrustmentService;

    @Autowired
    private InvestigateTaskService investigateTaskService;

    @MockBean
    private ApprovalUserHelper approvalUserHelper;

    @MockBean
    private EntrustmentMapper entrustmentMapper;

    private List<UserInfoDTO> mockUsers;

    @Before
    public void setUp() {
        // 初始化测试数据
        mockUsers = new ArrayList<>();
        UserInfoDTO user1 = new UserInfoDTO();
        user1.setUserCode("USER001");
        user1.setUserName("测试用户1");
        user1.setComCode("DEPT001");
        user1.setComName("测试部门1");
        mockUsers.add(user1);

        UserInfoDTO user2 = new UserInfoDTO();
        user2.setUserCode("USER002");
        user2.setUserName("测试用户2");
        user2.setComCode("DEPT001");
        user2.setComName("测试部门1");
        mockUsers.add(user2);
    }

    @Test
    public void testEntrustmentService_GetApprovalUsers() throws GlobalBusinessException {
        // 准备测试数据
        String reportNo = "REPORT001";
        Integer caseTimes = 1;
        String departmentCode = "DEPT001";

        // Mock 依赖
        when(entrustmentMapper.getDepartmentCodeByReportNo(reportNo, caseTimes)).thenReturn(departmentCode);
        when(approvalUserHelper.getApprovalUsers(departmentCode)).thenReturn(mockUsers);

        // 执行测试
        List<UserInfoDTO> result = entrustmentService.getApprovalUsers(reportNo, caseTimes);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("USER001", result.get(0).getUserCode());
        assertEquals("USER002", result.get(1).getUserCode());

        // 验证方法调用
        verify(entrustmentMapper).getDepartmentCodeByReportNo(reportNo, caseTimes);
        verify(approvalUserHelper).getApprovalUsers(departmentCode);
    }

    @Test
    public void testInvestigateTaskService_GetApprovalUsers_WithoutInitFlag() throws GlobalBusinessException {
        // 准备测试数据
        String reportNo = "REPORT001";
        Integer caseTimes = 1;
        String initFlag = "1";
        String currentDepartmentCode = "DEPT001";

        // Mock 依赖
        when(approvalUserHelper.getApprovalUsers(currentDepartmentCode)).thenReturn(mockUsers);

        // Mock WebServletContext
        try (MockedStatic<WebServletContext> mockedWebServletContext = Mockito.mockStatic(WebServletContext.class)) {
            mockedWebServletContext.when(WebServletContext::getDepartmentCode).thenReturn(currentDepartmentCode);

            // 执行测试
            List<UserInfoDTO> result = investigateTaskService.getApprovalUsers(reportNo, caseTimes, initFlag);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("USER001", result.get(0).getUserCode());
            assertEquals("USER002", result.get(1).getUserCode());

            // 验证方法调用
            verify(approvalUserHelper).getApprovalUsers(currentDepartmentCode);
            verify(approvalUserHelper, never()).excludeUsers(any(), any());
        }
    }

    @Test
    public void testInvestigateTaskService_GetApprovalUsers_WithInitFlag2() throws GlobalBusinessException {
        // 准备测试数据
        String reportNo = "REPORT001";
        Integer caseTimes = 1;
        String initFlag = "2";
        String currentDepartmentCode = "DEPT001";
        List<UserInfoDTO> filteredUsers = mockUsers.subList(0, 1); // 只返回第一个用户

        // Mock 依赖
        when(approvalUserHelper.getApprovalUsers(currentDepartmentCode)).thenReturn(mockUsers);
        when(approvalUserHelper.excludeUsers(eq(mockUsers), anyList())).thenReturn(filteredUsers);

        // Mock WebServletContext
        try (MockedStatic<WebServletContext> mockedWebServletContext = Mockito.mockStatic(WebServletContext.class)) {
            mockedWebServletContext.when(WebServletContext::getDepartmentCode).thenReturn(currentDepartmentCode);

            // 执行测试
            List<UserInfoDTO> result = investigateTaskService.getApprovalUsers(reportNo, caseTimes, initFlag);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("USER001", result.get(0).getUserCode());

            // 验证方法调用
            verify(approvalUserHelper).getApprovalUsers(currentDepartmentCode);
            verify(approvalUserHelper).excludeUsers(eq(mockUsers), anyList());
        }
    }

    @Test(expected = GlobalBusinessException.class)
    public void testEntrustmentService_GetApprovalUsers_ThrowsException() throws GlobalBusinessException {
        // 准备测试数据
        String reportNo = "REPORT001";
        Integer caseTimes = 1;
        String departmentCode = "DEPT001";

        // Mock 异常情况
        when(entrustmentMapper.getDepartmentCodeByReportNo(reportNo, caseTimes)).thenReturn(departmentCode);
        when(approvalUserHelper.getApprovalUsers(departmentCode)).thenThrow(new RuntimeException("测试异常"));

        // 执行测试，期望抛出异常
        entrustmentService.getApprovalUsers(reportNo, caseTimes);
    }

    @Test(expected = GlobalBusinessException.class)
    public void testInvestigateTaskService_GetApprovalUsers_ThrowsException() throws GlobalBusinessException {
        // 准备测试数据
        String reportNo = "REPORT001";
        Integer caseTimes = 1;
        String initFlag = "1";
        String currentDepartmentCode = "DEPT001";

        // Mock 异常情况
        when(approvalUserHelper.getApprovalUsers(currentDepartmentCode)).thenThrow(new RuntimeException("测试异常"));

        // Mock WebServletContext
        try (MockedStatic<WebServletContext> mockedWebServletContext = Mockito.mockStatic(WebServletContext.class)) {
            mockedWebServletContext.when(WebServletContext::getDepartmentCode).thenReturn(currentDepartmentCode);

            // 执行测试，期望抛出异常
            investigateTaskService.getApprovalUsers(reportNo, caseTimes, initFlag);
        }
    }
}
