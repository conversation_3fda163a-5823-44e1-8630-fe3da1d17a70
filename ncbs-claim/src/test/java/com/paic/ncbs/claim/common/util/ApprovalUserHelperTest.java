package com.paic.ncbs.claim.common.util;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import com.paic.ncbs.claim.common.response.ResponseResult;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ApprovalUserHelper 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ApprovalUserHelperTest {

    @InjectMocks
    private ApprovalUserHelper approvalUserHelper;

    @Mock
    private InvestigateService investigateService;

    @Mock
    private DepartmentDefineMapper departmentDefineMapper;

    @Mock
    private TaskPoolService taskPoolService;

    private List<UserInfoDTO> mockUsers;
    private List<ServerInfoVO> mockServerInfoList;
    private TpaServerInfoListVO mockTpaServerInfoListVO;

    @Before
    public void setUp() {
        // 初始化测试数据
        mockUsers = new ArrayList<>();
        UserInfoDTO user1 = new UserInfoDTO();
        user1.setUserCode("USER001");
        user1.setUserName("测试用户1");
        user1.setComCode("DEPT001");
        user1.setComName("测试部门1");
        mockUsers.add(user1);

        UserInfoDTO user2 = new UserInfoDTO();
        user2.setUserCode("USER002");
        user2.setUserName("测试用户2");
        user2.setComCode("DEPT002");
        user2.setComName("测试部门2");
        mockUsers.add(user2);

        // 初始化服务器信息
        mockServerInfoList = new ArrayList<>();
        ServerInfoVO serverInfo = new ServerInfoVO();
        serverInfo.setServerCode("SERVER001");
        mockServerInfoList.add(serverInfo);

        mockTpaServerInfoListVO = new TpaServerInfoListVO();
        mockTpaServerInfoListVO.setServerInfoList(mockServerInfoList);
    }

    @Test
    public void testGetApprovalUsers_Success() throws GlobalBusinessException {
        // 准备测试数据
        String departmentCode = "DEPT001";
        List<String> childCodeList = Arrays.asList("DEPT001", "DEPT002");

        // Mock 外部依赖
        ResponseResult<TpaServerInfoListVO> responseResult = ResponseResult.success(mockTpaServerInfoListVO);
        when(investigateService.getServerInfoList()).thenReturn(responseResult);
        when(departmentDefineMapper.getChildCodeList(anyList())).thenReturn(Arrays.asList("DEPT002"));
        when(departmentDefineMapper.queryDepartmentNameByDeptCode("DEPT001")).thenReturn("测试部门1");
        when(departmentDefineMapper.queryDepartmentNameByDeptCode("DEPT002")).thenReturn("测试部门2");
        when(taskPoolService.searchTaskDealUser(eq("DEPT001"), eq(BpmConstants.OC_MAJOR_INVESTIGATE)))
                .thenReturn(Arrays.asList(mockUsers.get(0)));
        when(taskPoolService.searchTaskDealUser(eq("DEPT002"), eq(BpmConstants.OC_MAJOR_INVESTIGATE)))
                .thenReturn(Arrays.asList(mockUsers.get(1)));

        // Mock WebServletContext
        try (MockedStatic<WebServletContext> mockedWebServletContext = Mockito.mockStatic(WebServletContext.class)) {
            mockedWebServletContext.when(WebServletContext::getDepartmentCode).thenReturn("DEPT001");

            // 执行测试
            List<UserInfoDTO> result = approvalUserHelper.getApprovalUsers(departmentCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("USER001", result.get(0).getUserCode());
            assertEquals("DEPT001", result.get(0).getComCode());
        }
    }

    @Test
    public void testExcludeUsers_WithExcludeList() {
        // 准备测试数据
        List<String> excludeUserCodes = Arrays.asList("USER001");

        // 执行测试
        List<UserInfoDTO> result = approvalUserHelper.excludeUsers(mockUsers, excludeUserCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("USER002", result.get(0).getUserCode());
    }

    @Test
    public void testExcludeUsers_WithEmptyExcludeList() {
        // 准备测试数据
        List<String> excludeUserCodes = new ArrayList<>();

        // 执行测试
        List<UserInfoDTO> result = approvalUserHelper.excludeUsers(mockUsers, excludeUserCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    public void testExcludeUsers_WithNullExcludeList() {
        // 执行测试
        List<UserInfoDTO> result = approvalUserHelper.excludeUsers(mockUsers, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test(expected = GlobalBusinessException.class)
    public void testGetApprovalUsers_ThrowsException() throws GlobalBusinessException {
        // Mock 异常情况
        when(investigateService.getServerInfoList()).thenThrow(new RuntimeException("测试异常"));

        // 执行测试，期望抛出异常
        approvalUserHelper.getApprovalUsers("DEPT001");
    }
}
