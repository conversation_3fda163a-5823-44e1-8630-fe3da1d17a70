package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayBaseInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.settle.BatchPayAmountVo;
import com.paic.ncbs.claim.model.vo.settle.RiskWarningVO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;

import java.util.List;

public interface SettleService {

    List<DutyDetailPayDTO> getAutoSettleAmount(DutyPayDTO dutyPayDTO);

    BatchPayAmountVo getBatchPayAmount(SettlesFormVO settlesFormVO);
    
    List<PolicyPayBaseInfoDTO> getPolicyPayBaseInfo(String  reportNo, Integer caseTimes);

    /**
     * 分摊费用至险种责任
     */
    List<PolicyPayDTO> distributeFee(List<PolicyPayDTO> policyPays, String claimType, Integer subTimes) ;

    /**
     * 理算提交
     * @param settlesFormDTO
     */
   void settle(SettlesFormVO settlesFormDTO);


    /**
     * 获取风险预警信息
     * @param reportNo
     * @param caseTimes
     */
    RiskWarningVO getRiskWarning(String reportNo, Integer caseTimes);

    RiskWarningVO getRiskWarningVerify(String reportNo, Integer caseTimes);

    /**
     * 获取短信模版
     */
    ClmsSmsTemplateDTO getSmsTemplate(String templateClass);

    /**
     * 修改短信模版
     */
    void modifySmsTemplate(String templateDesc,String templateClass);

}
