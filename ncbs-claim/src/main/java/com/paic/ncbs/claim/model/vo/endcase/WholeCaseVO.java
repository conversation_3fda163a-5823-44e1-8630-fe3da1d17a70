package com.paic.ncbs.claim.model.vo.endcase;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

//@JsonIgnoreProperties({"createdBy", "createdDate", "updatedDate", "updatedBy", "currentPage", "perPageSize",
//        "pageNumEnd", "pageNumBegin" })
@ApiModel("WholeCaseVO-整案VO")
@Accessors(chain = true)
public class WholeCaseVO extends EntityDTO {
	

	private static final long serialVersionUID = -5101597512161953495L;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("赔付次数")
	private Integer caseTimes;

	@ApiModelProperty("赔付号")
	private String caseNo ;

	@ApiModelProperty("报案日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date reportDate;

	@ApiModelProperty("事故日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date accidentDate;

	@ApiModelProperty("姓名")
	private String name;

	@ApiModelProperty("被保险人")
	private String insuredName;

	@ApiModelProperty("客户类型")
	private String clientType;

	@ApiModelProperty("客户类型名称")
	private String clientTypeName;

	@ApiModelProperty("保单号")
	private String policyNo;

	@ApiModelProperty("生日")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date birthday;

	@ApiModelProperty("证件类型")
	private String certificateType;

	@ApiModelProperty("证书编号")
	private String certificateNo;

	@ApiModelProperty("案件状态名称")
	private String caseStatusName;

	@ApiModelProperty("流程状态名称")
	private String processStatusName;

	@ApiModelProperty("流程状态")
	private String processStatus;

	@ApiModelProperty("赔偿结论")
	private String indemnityConclusion;

	@ApiModelProperty("赔偿模式")
	private String indemnityModel;

	@ApiModelProperty("是否立案")
	private String isRegister;

	@ApiModelProperty("结案标识")
	private String endCaseFlag;

	@ApiModelProperty("结案日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date endCaseDate;

	@ApiModelProperty("结案金额")
	private BigDecimal endCaseAmount;

	@ApiModelProperty("预估金额")
	private BigDecimal estimateAmount;

	@ApiModelProperty("报案号列表")
	private List<String> reportNos;

	@ApiModelProperty("开始报案日期")
	private String beginReportDate;

	@ApiModelProperty("案件结束日期")
	private String endReportDate;

	@ApiModelProperty("核赔人")
	private String verifyUm;

	@ApiModelProperty("电子号")
	private String electronicNo;

	@ApiModelProperty("报案类型")
	private String reportType;

	@ApiModelProperty("特殊案例类型")
	private String specialCaseType;

	@ApiModelProperty("部门编码")
    private String  departmentCode;

	@ApiModelProperty("结案开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date  endCaseDateBegin;

	@ApiModelProperty("结案结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date  endCaseDateEnd;

	@ApiModelProperty("支付状态")
    private String  payStatus;

	@ApiModelProperty("文件类型")
	private String docType;

	@ApiModelProperty("案件参数VO")
    private List<CaseParamVO> caseList;

	@ApiModelProperty("用户id")
    private String userId;

	@ApiModelProperty("案件批次号")
    private String reportBatchNo;

	@ApiModelProperty("idAhcs风险目标")
    private String idAhcsRiskTarget;

	@ApiModelProperty("损失对象编号")
    private String lossObjectNo;

	private String personnelAttribute = "";

	private String assigner;

	private List<String> departmentCodes;

	@ApiModelProperty("客户号")
	private String clientNo;

	@ApiModelProperty("预报案号")
	private String acceptanceNo;

	@ApiModelProperty("是否最新（决定是否可以重开）")
	private String isNewest;

	@ApiModelProperty("文件ID")
	private String fileId;

	@ApiModelProperty("分支流程状态标识")
	private String subProcessFlag;

	@ApiModelProperty("案件录入时长")
	private BigDecimal caseInputDuration;

	@ApiModelProperty("模型识别案件：是/否")
	private String isAIModel;

	public BigDecimal getCaseInputDuration() {
		return caseInputDuration;
	}

	public void setCaseInputDuration(BigDecimal caseInputDuration) {
		this.caseInputDuration = caseInputDuration;
	}

	public String getIsAIModel() {
		return isAIModel;
	}

	public void setIsAIModel(String isAIModel) {
		this.isAIModel = isAIModel;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getAcceptanceNo() {
		return acceptanceNo;
	}

	public void setAcceptanceNo(String acceptanceNo) {
		this.acceptanceNo = acceptanceNo;
	}

	public String getSubProcessFlag() {
		return subProcessFlag;
	}

	public void setSubProcessFlag(String subProcessFlag) {
		this.subProcessFlag = subProcessFlag;
	}

	public String getIsNewest() {
		return isNewest;
	}

	public void setIsNewest(String isNewest) {
		this.isNewest = isNewest;
	}

	public List<String> getDepartmentCodes() {
		return departmentCodes;
	}

	public void setDepartmentCodes(List<String> departmentCodes) {
		this.departmentCodes = departmentCodes;
	}

	public String getLossObjectNo() {
		return lossObjectNo;
	}

	public void setLossObjectNo(String lossObjectNo) {
		this.lossObjectNo = lossObjectNo;
	}

	public String getReportBatchNo() {
		return reportBatchNo;
	}

	public void setReportBatchNo(String reportBatchNo) {
		this.reportBatchNo = reportBatchNo;
	}

	public List<CaseParamVO> getCaseList() {
		return caseList;
	}

	public void setCaseList(List<CaseParamVO> caseList) {
		this.caseList = caseList;
	}

	public BigDecimal getEstimateAmount() {
		return estimateAmount;
	}

	public void setEstimateAmount(BigDecimal estimateAmount) {
		this.estimateAmount = estimateAmount;
	}

	public BigDecimal getEndCaseAmount() {
		return endCaseAmount;
	}

	public void setEndCaseAmount(BigDecimal endCaseAmount) {
		this.endCaseAmount = endCaseAmount;
	}

	public String getProcessStatusName() {
		return processStatusName;
	}

	public void setProcessStatusName(String processStatusName) {
		this.processStatusName = processStatusName;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public String getElectronicNo() {
		return electronicNo;
	}

	public void setElectronicNo(String electronicNo) {
		this.electronicNo = electronicNo;
	}

	public String getVerifyUm() {
		return verifyUm;
	}

	public void setVerifyUm(String verifyUm) {
		this.verifyUm = verifyUm;
	}

	public List<String> getReportNos() {
		return reportNos;
	}

	public void setReportNos(List<String> reportNos) {
		this.reportNos = reportNos;
	}

	public String getIsRegister() {
		return isRegister;
	}

	public void setIsRegister(String isRegister) {
		this.isRegister = isRegister;
	}

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public Integer getCaseTimes() {
		return caseTimes;
	}

	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getClientType() {
		return clientType;
	}

	public void setClientType(String clientType) {
		this.clientType = clientType;
	}



	public String getClientTypeName() {
		return clientTypeName;
	}

	public void setClientTypeName(String clientTypeName) {
		this.clientTypeName = clientTypeName;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getCaseStatusName() {
		return caseStatusName;
	}

	public void setCaseStatusName(String caseStatusName) {
		this.caseStatusName = caseStatusName;
	}

	public String getIndemnityConclusion() {
		return indemnityConclusion;
	}

	public void setIndemnityConclusion(String indemnityConclusion) {
		this.indemnityConclusion = indemnityConclusion;
	}

	public String getIndemnityModel() {
		return indemnityModel;
	}

	public void setIndemnityModel(String indemnityModel) {
		this.indemnityModel = indemnityModel;
	}

	public String getEndCaseFlag() {
		return endCaseFlag;
	}

	public void setEndCaseFlag(String endCaseFlag) {
		this.endCaseFlag = endCaseFlag;
	}

	public String getProcessStatus() {
		return processStatus;
	}

	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}

	public String getBeginReportDate() {
		return beginReportDate;
	}

	public void setBeginReportDate(String beginReportDate) {
		this.beginReportDate = beginReportDate;
	}

	public String getEndReportDate() {
		return endReportDate;
	}

	public void setEndReportDate(String endReportDate) {
		this.endReportDate = endReportDate;
	}

	public String getSpecialCaseType() {
		return specialCaseType;
	}

	public void setSpecialCaseType(String specialCaseType) {
		this.specialCaseType = specialCaseType;
	}

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public Date getEndCaseDateBegin() {
        return endCaseDateBegin;
    }

    public void setEndCaseDateBegin(Date endCaseDateBegin) {
        this.endCaseDateBegin = endCaseDateBegin;
    }

    public Date getEndCaseDateEnd() {
        return endCaseDateEnd;
    }

    public void setEndCaseDateEnd(Date endCaseDateEnd) {
        this.endCaseDateEnd = endCaseDateEnd;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getIdAhcsRiskTarget() {
		return idAhcsRiskTarget;
	}

	public void setIdAhcsRiskTarget(String idAhcsRiskTarget) {
		this.idAhcsRiskTarget = idAhcsRiskTarget;
	}

	public String getPersonnelAttribute() {
		return personnelAttribute;
	}

	public void setPersonnelAttribute(String personnelAttribute) {
		this.personnelAttribute = personnelAttribute;
	}

	public Date getReportDate() {
		return reportDate;
	}

	public void setReportDate(Date reportDate) {
		this.reportDate = reportDate;
	}

	public Date getAccidentDate() {
		return accidentDate;
	}

	public void setAccidentDate(Date accidentDate) {
		this.accidentDate = accidentDate;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public Date getEndCaseDate() {
		return endCaseDate;
	}

	public void setEndCaseDate(Date endCaseDate) {
		this.endCaseDate = endCaseDate;
	}

	public String getAssigner() {
		return assigner;
	}

	public void setAssigner(String assigner) {
		this.assigner = assigner;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType;
	}

	public String getClientNo() {
		return clientNo;
	}

	public void setClientNo(String clientNo) {
		this.clientNo = clientNo;
	}
}
