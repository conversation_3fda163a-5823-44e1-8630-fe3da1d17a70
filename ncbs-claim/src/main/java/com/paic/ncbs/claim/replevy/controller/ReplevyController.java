package com.paic.ncbs.claim.replevy.controller;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.RedisKeyConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.service.ReplevyService;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyTextVo;
import com.paic.ncbs.claim.replevy.vo.ReplevyApiVo;
import com.paic.ncbs.claim.replevy.vo.ReplevyReqApiVo;
import com.paic.ncbs.claim.replevy.vo.SendCashFlowSearchVo;
import com.paic.ncbs.claim.service.user.PermissionService;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 追偿前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/replevy/replevyAction")
public class ReplevyController {

    @Autowired
    private ReplevyChargeService replevyChargeService;
    @Autowired
    private ReplevyService replevyService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PermissionService permissionService;
    @ApiOperation("追偿主页面保存提交")
    @RequestMapping(value = "/saveOrSubmitReplevy", method = RequestMethod.POST)
    public  ResponseResult<Object> saveOrSubmitReplevy(@RequestBody ReplevyApiVo replevyApiVo) {
        return  replevyService.saveOrSubmitReplevy(replevyApiVo);
    }

    @ApiOperation("追偿子页面保存提交")
    @RequestMapping(value = "/saveReplevyDetail", method = RequestMethod.POST)
    public  ResponseResult<Object> saveReplevyDetail(@RequestBody ReplevyApiVo replevyApiVo) {
        return  replevyService.saveReplevyDetail(replevyApiVo);
    }

    @ApiOperation("直接理赔费用提交")
    @RequestMapping(value = "/saveOrSubmitReplevyFee", method = RequestMethod.POST)
    public  ResponseResult<Object> saveOrSubmitReplevyFee(@RequestBody ReplevyApiVo replevyApiVo) {
        return  replevyService.saveOrSubmitReplevyFee(replevyApiVo);
    }

    @ApiOperation("关联实收调用收付流水查询")
    @RequestMapping(value = "/sendCashFlowSearch", method = RequestMethod.POST)
    public  ResponseResult<Object> sendCashFlowSearch(@RequestBody SendCashFlowSearchVo sendCashFlowSearchVo) {
        return  replevyService.sendCashFlowSearch(sendCashFlowSearchVo);
    }
    @ApiOperation(value = "追偿费用审批")
    @PostMapping(value = "/sendRelevyFeeAudit")
    public ResponseResult<Object> sendRelevyFeeAudit(@RequestBody ClmsReplevyTextVo clmsReplevyTextVo) throws Exception {
        LogUtil.info("sendRelevyFeeAudit进入追偿费用审批追偿号={}，追偿次数={},入参={}", clmsReplevyTextVo.getReplevyNo(), clmsReplevyTextVo.getReplevyTimes(), JSON.toJSONString(clmsReplevyTextVo));
        String key = RedisKeyConstants.RELEVY_FEE_SAVE_LOCK + clmsReplevyTextVo.getReplevyNo() + ":" + clmsReplevyTextVo.getReplevyTimes();
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock()) {
                List<String> msgList = new ArrayList<>();
                replevyChargeService.sendRelevyFeeAudit(clmsReplevyTextVo, msgList);
                LogUtil.info("#追偿费用审批完成,追偿号={}，追偿次数={}", clmsReplevyTextVo.getReplevyNo(), clmsReplevyTextVo.getReplevyTimes());
                if (msgList.isEmpty()) {
                    return ResponseResult.success("");
                }
                return ResponseResult.success(msgList.get(0));
            } else {
                throw new GlobalBusinessException("追偿费用审批正在处理中！");
            }
        }finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    @ApiOperation(value = "追偿审批")
    @PostMapping(value = "/sendRelevyAudit")
    public ResponseResult<Object> sendRelevyAudit(@RequestBody ClmsReplevyTextVo clmsReplevyTextVo) throws Exception {
        LogUtil.info("sendRelevyAudit进入追偿审批追偿={},入参={}",clmsReplevyTextVo.getReplevyNo(), clmsReplevyTextVo.getReplevyTimes(), JSON.toJSONString(clmsReplevyTextVo));
        String key = RedisKeyConstants.RELEVY_SAVE_LOCK + clmsReplevyTextVo.getReplevyNo() + ":" + clmsReplevyTextVo.getReplevyTimes();
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock()) {
                List<String> msgList = new ArrayList<>();
                replevyChargeService.sendRelevyAudit(clmsReplevyTextVo, msgList);
                LogUtil.info("#追偿审批完成,报案号={}，追偿次数={}", clmsReplevyTextVo.getReportNo(), clmsReplevyTextVo.getReplevyTimes());
                if (msgList.isEmpty()) {
                    return ResponseResult.success("");
                }
                return ResponseResult.success(msgList.get(0));
            } else {
                throw new GlobalBusinessException("追偿审批正在处理中！");
            }
        }finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation("追偿页面初始化")
    @RequestMapping(value = "/initReplevy", method = RequestMethod.POST)
    public  ResponseResult<Object> initReplevy(@RequestBody ReplevyApiVo replevyApiVo) {
        return  replevyService.initReplevy(replevyApiVo);
    }

    @ApiOperation("追偿审核页面初始化")
    @RequestMapping(value = "/initReplevyApprove", method = RequestMethod.POST)
    public  ResponseResult<Object> initReplevyApprove(@RequestBody ReplevyApiVo replevyApiVo) {
        return  replevyService.initReplevyApprove(replevyApiVo);
    }

    @ApiOperation("查看按钮页面初始化")
    @RequestMapping(value = "/initReplevyView", method = RequestMethod.POST)
    public  ResponseResult<Object> initReplevyView(@RequestBody ReplevyApiVo replevyApiVo) {
        return  replevyService.initReplevyView(replevyApiVo);
    }

    @ApiOperation("统一删除追偿相关数据")
    @RequestMapping(value = "/deleteReplevyData", method = RequestMethod.POST)
    public ResponseResult<Object> deleteReplevyData(@RequestBody ReplevyApiVo replevyApiVo) {
        return replevyService.deleteReplevyData(replevyApiVo);
    }

    @ApiOperation("查询险种和责任信息")
    @RequestMapping(value = "/queryPlanAndDutyInfo", method = RequestMethod.POST)
    public ResponseResult<Object> queryPlanAndDutyInfo(@RequestBody ReplevyApiVo replevyApiVo) {
        return replevyService.queryPlanAndDutyInfo(replevyApiVo);
    }

    @ApiOperation("根据报案号查询追偿主表信息")
    @RequestMapping(value = "/queryReplevyMainByReportNo", method = RequestMethod.POST)
    public ResponseResult<Object> queryReplevyMainByReportNo(@RequestBody ReplevyApiVo replevyApiVo) {
        return replevyService.queryReplevyMainByReportNo(replevyApiVo);
    }
    @ApiOperation("发起追偿前校验")
    @RequestMapping(value = "/checkReplevy", method = RequestMethod.POST)
    public ResponseResult<Object> checkReplevy(@RequestBody ReplevyReqApiVo replevyReqApiVo) {
        return replevyService.checkReplevy(replevyReqApiVo);
    }
    @ApiOperation("获取追偿上级用户")
    @GetMapping(value = "/getReplevyUserList")
    public ResponseResult<List<PermissionUserDTO>> getReplevyUserList(@RequestParam("reportNo") String reportNo,
                                                                     @RequestParam("caseTimes")Integer caseTimes,
                                                                     @RequestParam("taskId")String taskId){
        return ResponseResult.success(permissionService.getReplevyUserList(reportNo,caseTimes,taskId));
    }

}
