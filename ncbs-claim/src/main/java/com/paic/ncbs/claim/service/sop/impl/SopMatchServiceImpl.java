package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopFileMapper;
import com.paic.ncbs.claim.model.vo.sop.SopFileVO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.sop.SopMainService;
import com.paic.ncbs.claim.service.sop.SopMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * SOP匹配服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopMatchServiceImpl implements SopMatchService {

    @Autowired
    private SopMainService sopMainService;

    @Autowired
    private ClmsSopFileMapper clmsSopFileMapper;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Override
    public List<SopMainVO> matchSopByCase(String reportNo, Integer caseTimes, String taskBpmKey) {
        log.info("根据案件信息匹配SOP规则，reportNo：{}，caseTimes：{}，taskBpmKey：{}",
                reportNo, caseTimes, taskBpmKey);

        if (RapeStringUtils.isEmptyStr(reportNo)) {
            log.warn("报案号为空，无法匹配SOP");
            return new ArrayList<>();
        }

        try {
            List<SopMainVO> matchedSops = sopMainService.matchSopRulesByCase(reportNo, caseTimes, taskBpmKey);

            if (RapeCollectionUtils.isNotEmpty(matchedSops)) {
                for (SopMainVO sopMainVO : matchedSops) {
                    try {
                        List<ClmsSopFile> fileEntityList = clmsSopFileMapper.selectByIdSopMain(sopMainVO.getIdSopMain());

                        if (RapeCollectionUtils.isNotEmpty(fileEntityList)) {
                            List<SopFileVO> fileList = new ArrayList<>();
                            for (ClmsSopFile fileEntity : fileEntityList) {
                                SopFileVO fileVO = new SopFileVO();
                                BeanUtils.copyProperties(fileEntity, fileVO);
                                try {
                                    String downloadUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileEntity.getFileId(), fileEntity.getFileName());
                                    fileVO.setFileUrl(downloadUrl);
                                } catch (Exception e) {
                                    log.error("获取文件下载地址失败，fileId：{}", fileEntity.getFileId(), e);
                                }
                                fileList.add(fileVO);
                            }
                            sopMainVO.setFileList(fileList);
                        }
                    } catch (Exception e) {
                        log.error("查询SOP文件信息失败，idSopMain：{}", sopMainVO.getIdSopMain(), e);
                    }
                }
            }

            log.info("匹配到{}条SOP规则", matchedSops.size());
            return matchedSops;

        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
            return new ArrayList<>();
        }
    }

}
