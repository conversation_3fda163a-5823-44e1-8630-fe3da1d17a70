package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.taskdeal.GroupInfoVO;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.sop.SopMainService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * SOP管理Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP管理")
@RestController
@RequestMapping("/mng/app/sopManageAction")
@Slf4j
public class SopManageController extends BaseController {

    @Autowired
    private SopMainService sopMainService;

    @Autowired
    private TaskListService taskListService;

    @ApiOperation("分页查询SOP列表")
    @PostMapping(value = "/getSopList")
    public ResponseResult<List<SopMainVO>> getSopList(@RequestBody SopQueryVO queryVO) {
        try {
            LogUtil.audit("分页查询SOP列表，查询条件：{}", queryVO);
            List<SopMainVO> result = sopMainService.getSopList(queryVO);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("分页查询SOP列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取SOP详情")
    @GetMapping(value = "/getSopDetail/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<SopMainVO> getSopDetail(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("获取SOP详情，idSopMain：{}", idSopMain);
            SopMainVO result = sopMainService.getSopDetail(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取SOP详情失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("保存或更新SOP")
    @PostMapping(value = "/saveOrUpdateSop")
    @ResponseBody
    public ResponseResult<String> saveOrUpdateSop(SopMainDTO sopMainDTO, HttpServletRequest request) {
        try {
            LogUtil.audit("保存或更新SOP，SOP名称：{}，操作类型：{}", sopMainDTO.getSopName(), sopMainDTO.getInitFlag());
            String result = sopMainService.saveOrUpdateSop(sopMainDTO, request);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("保存或更新SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("校验SOP名称是否重复")
    @GetMapping(value = "/checkSopNameExists")
    @ApiImplicitParam(name = "sopName", value = "SOP名称", required = true, dataType = "String", paramType = "query")
    public ResponseResult<Boolean> checkSopNameExists(@RequestParam("sopName") String sopName) {
        try {
            LogUtil.audit("校验SOP名称是否重复，sopName：{}", sopName);
            boolean result = sopMainService.checkSopNameExists(sopName);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("校验SOP名称是否重复失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取险种信息列表")
    @GetMapping(value = "/getPlanInfoList")
    public ResponseResult<List<Object>> getPlanInfoList() {
        try {
            LogUtil.audit("获取险种信息列表");
            List<Object> result = sopMainService.getPlanInfoList();
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取险种信息列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }
    
    @ApiOperation("获取环节下拉框数据")
    @GetMapping(value = "/getTaskDropdownList")
    public ResponseResult<List<Map<String, String>>> getTaskDropdownList() {
        try {
            LogUtil.audit("获取环节下拉框数据");
            List<Map<String, String>> result = sopMainService.getTaskDropdownList();
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取环节下拉框数据失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("根据机构号和岗位权限查询人员信息列表")
    @GetMapping(value = "/getUserInfoBySOP")
    public ResponseResult<List<Map<String, String>>> getUserInfoBySOP() {
        try {
            LogUtil.audit("根据机构号和岗位权限查询人员信息列表");
            List<Map<String, String>> result = sopMainService.getUserInfoBySOP();
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据机构号和岗位权限查询人员信息列表失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @PostMapping(value = "/getAllRiskGroupInfo")
    @ApiOperation(value = "获取所有方案名称")
    public ResponseResult<List<GroupInfoVO>> getAllRiskGroupInfo(@RequestBody Map<String, String> map) {
        List<GroupInfoVO> groupInfoVOList = taskListService.getAllRiskGroupInfo(map);
        if (groupInfoVOList != null && !groupInfoVOList.isEmpty()) {
            for (GroupInfoVO groupInfoVO : groupInfoVOList) {
                try {
                    String packageCode = groupInfoVO.getPackageCode();
                    if (packageCode != null && packageCode.contains("_")) {
                        String[] parts = packageCode.split("_");
                        if (parts.length >= 2) {
                            groupInfoVO.setPackageCode(parts[1]);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理packageCode时发生异常，保留原值。packageCode: {}", groupInfoVO.getPackageCode(), e);
                }
            }
        }
        return ResponseResult.success(groupInfoVOList);
    }

}