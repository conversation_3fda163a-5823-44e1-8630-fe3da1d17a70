package com.paic.ncbs.claim.model.dto.reinsurance;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ReinsuranceReqDTO {
    /**
     * 案件号
     */
    private String claimNo;
    /**
     * 再保类型
     */
    private String claimType;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 再保人代码
     */
    private String reinsCode;
    /**
     * 出险日期
     */
    private String date;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 立案/估损/理算提交人
     */
    private String submitter;
}
