package com.paic.ncbs.claim.common.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础字符、数字
 */
public class BaseConstant {

	public static final boolean TRUE = true;
	public static final boolean FALSE = false;

	public static final int INT_0 = 0;
	public static final int INT_1 = 1;
	public static final int INT_2 = 2;
	public static final int INT_3 = 3;
	public static final int INT_4 = 4;
	public static final int INT_5 = 5;
	public static final int INT_6 = 6;
	public static final int INT_7 = 7;
	public static final int INT_8 = 8;
	public static final int INT_9 = 9;
	public static final int INT_10 = 10;


	public static final String STRING_0 = "0";
	public static final String STRING_1 = "1";
	public static final String STRING_2 = "2";
	public static final String STRING_3 = "3";
	public static final String STRING_4 = "4";
	public static final String STRING_5 = "5";
	public static final String STRING_6 = "6";
	public static final String STRING_7 = "7";
	public static final String STRING_8 = "8";
	public static final String STRING_9 = "9";
	public static final String STRING_10 = "10";
	public static final String STRING_11 = "11";
	public static final String STRING_13 = "13";
	public static final String STRING_20 = "20";

	public static final String STRING_00 = "00";
	public static final String STRING_01 = "01";
	public static final String STRING_02 = "02";
	public static final String STRING_03 = "03";
	public static final String STRING_04 = "04";
	public static final String STRING_05 = "05";
	public static final String STRING_06 = "06";
	public static final String STRING_07 = "07";
	public static final String STRING_08 = "08";
	public static final String STRING_09 = "09";


	public static final Long  LONG_ONE = 1L;

	public static final String UPPER_CASE_A = "A";

	public static final String UPPER_CASE_F = "F";

	public static final String UPPER_CASE_I = "I";

	public static final String UPPER_CASE_N = "N";

	public static final String UPPER_CASE_Y = "Y";
	public static final String UPPER_CASE_Z = "Z";

	public static final String LOWER_CASE_Y = "y";

	public static final String SYSTEM = "SYSTEM";

	public static final String REQUEST_ID = "requestId";

	public static final String STRING_000 = "000";

	public static final String STRING_10000 = "10000";

	public static final String STRING_0000 = "0000";

	public static final String STRING_AAAA = "AAAA";

	public static final String STRING_YYMMDD = "yyMMdd";

	public static final String DUTY_CODE_CVJE003 = "CVJE003";

	public static final String SUCCOR_NO_CODE_REDIS_KEY = "report:succorNoIncrementCode";

	public static final String  SEPARATE_CHAR = "|";

	public static final String  AGENT_NAME = "agentName";

	public static final Long  ONE_DAY = 86400L;

	public static final String OTHER_CITY_CODE = "999991";
	public static final String OTHER_COUNTRY_CODE = "999992";

	public static final String CITY = "city";
	public static final String PROVINCE = "province";

    public static final String USER_ID = "userId";

    // 调用反洗钱接口的默认值
    public static final String AML_DEFAULT_VALUE= "@N";

	/**
	 * 计算剩余赔付额的场景——历史案件页面
	 */
	public static final String MAX_PAY_SCENE_HISTORY = "history";

	/**
	 * 计算剩余赔付额的场景——打印通知书
	 */
	public static final String MAX_PAY_SCENE_PRINT = "print";

	/**
	 * Y-是
	 */
	public static final String BEIN_HOSPITAL_FLAG="Y";

    private BaseConstant(){}

	public static List<String> RISK_PROPERTY_PAY_TYPES = new ArrayList<>();
	public static String TARGET_TYPE_EMPLOYER = "996"; // 雇主责任
	public static String TARGET_TYPE_MEDICAL = "04001"; // 医疗责任险
	public static String TARGET_TYPE_APARTMENT = "995"; // 公寓责任
	public static String TARGET_TYPE_EXHIBITION = "04002"; // 展会责任险
	public static String TARGET_TYPE_USE_PACKAGE_CONFIG = "USE_PACKAGE_CONFIG"; // 使用方案配置
	static {
		//雇主责任险标的类型
		RISK_PROPERTY_PAY_TYPES.add(TARGET_TYPE_EMPLOYER);
		//医疗责任险标的类型
		RISK_PROPERTY_PAY_TYPES.add(TARGET_TYPE_MEDICAL);
		//公寓责任
		RISK_PROPERTY_PAY_TYPES.add(TARGET_TYPE_APARTMENT);
		//展会责任险
		RISK_PROPERTY_PAY_TYPES.add(TARGET_TYPE_EXHIBITION);
		// 使用方案配置
		RISK_PROPERTY_PAY_TYPES.add(TARGET_TYPE_USE_PACKAGE_CONFIG);
		//公共责任险标的类型
//		RISK_PROPERTY_PAY_TYPES.add("818");
		//诉讼责任险标的类型
//		RISK_PROPERTY_PAY_TYPES.add("89");
		//如需支持更多标的类型，请联系承保系统提供枚举值

	}

	public static String POLICY_PAY_INIT_LOCK = "CLAIM_POLICY_PAY_INIT_LOCK:%s";
}
