package com.paic.ncbs.claim.controller.standard.common;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.common.HospitalAliasInfoEntity;
import com.paic.ncbs.claim.model.dto.api.LowCodeResquestDTO;
import com.paic.ncbs.claim.model.dto.api.QueryBaseDataRequestDataDto;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.checkloss.DiagnoseDefineDTO;
import com.paic.ncbs.claim.model.vo.checkloss.DiagnoseDefineVO;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.service.checkloss.DiagnoseDefineService;
import com.paic.ncbs.claim.service.checkloss.HospitalInfoService;
import com.paic.ncbs.claim.service.common.HospitalAliasInfoService;
import com.paic.ncbs.claim.service.other.ConfigService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "对外公共基本数据相关接口")
@RestController
@Validated
@RequestMapping("/public/common")
public class CommonApiController {
    @Autowired
    private DiagnoseDefineService diagnoseDefineService;
    @Autowired
    private HospitalInfoService hospitalInfoService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private HospitalAliasInfoService hospitalAliasInfoService;

    SimpleDateFormat dateFormat_day = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取码值数据
     * */
    @PostMapping("/getCodeValue")
    public ResponseResult<Object> addOuterMedicalBill(@RequestBody StandardRequestDTO standardRequestDTO) throws ParseException {
        if (null != standardRequestDTO && null != standardRequestDTO.getRequestData()) {
            String jsonString = JSONObject.toJSONString(standardRequestDTO.getRequestData());
            QueryBaseDataRequestDataDto queryBaseDataRequestDataDto = JSONObject.parseObject(jsonString, QueryBaseDataRequestDataDto.class);
            if (null != queryBaseDataRequestDataDto && StringUtils.isNotEmpty(queryBaseDataRequestDataDto.getCodeType())) {
                String codeType = queryBaseDataRequestDataDto.getCodeType();
                if ("CV-001".equals(codeType)) {
                    DiagnoseDefineDTO diagnoseDefineDTO = new DiagnoseDefineDTO();
                    diagnoseDefineDTO.setDiagnoseCode(queryBaseDataRequestDataDto.getCodeCode());
                    diagnoseDefineDTO.setDiagnoseName(queryBaseDataRequestDataDto.getCodeName());
                    List<DiagnoseDefineVO> diagnoseDefineList = new ArrayList<>();
                    if (null != queryBaseDataRequestDataDto.getPageIndex() && null != queryBaseDataRequestDataDto.getPageRows()) {
                        Pager pager = new Pager();
                        pager.setPageIndex(queryBaseDataRequestDataDto.getPageIndex());
                        pager.setPageRows(queryBaseDataRequestDataDto.getPageRows());
                        diagnoseDefineDTO.setPager(pager);
                        PageHelper.startPage(pager.getPageIndex(),pager.getPageRows(),true);
                        diagnoseDefineList = diagnoseDefineService.getDiagnoseDefineList(diagnoseDefineDTO);
                        pager.setPageIndex(queryBaseDataRequestDataDto.getPageIndex());
                        pager.setPageRows(queryBaseDataRequestDataDto.getPageRows());
                        diagnoseDefineDTO.setPager(pager);
                        PageInfo<DiagnoseDefineVO> pageInfo = new PageInfo<>(diagnoseDefineList);
                        diagnoseDefineDTO.getPager().setTotalRows((int)pageInfo.getTotal());
                        Map<String, Object> subMap = new HashMap<>();
                        subMap.put("list", diagnoseDefineList);
                        subMap.put("pager", diagnoseDefineDTO.getPager());
                        return ResponseResult.success(subMap);
                    } else {
                        diagnoseDefineList = diagnoseDefineService.getDiagnoseDefineList(diagnoseDefineDTO);
                        return ResponseResult.success(diagnoseDefineList);
                    }
                } else if ("CV-002".equals(codeType)) {
                    HospitalInfoVO hospitalInfoVO = new HospitalInfoVO();
                    hospitalInfoVO.setHospitalCode(queryBaseDataRequestDataDto.getCodeCode());
                    hospitalInfoVO.setHospitalName(queryBaseDataRequestDataDto.getCodeName());
                    hospitalInfoVO.setHospitalFullName(queryBaseDataRequestDataDto.getCodeFullName());
                    if (StringUtils.isNotEmpty(queryBaseDataRequestDataDto.getMaintainStartDate())) {
                        hospitalInfoVO.setMaintainStartDate(dateFormat_day.parse(queryBaseDataRequestDataDto.getMaintainStartDate()));
                    }
                    if (StringUtils.isNotEmpty(queryBaseDataRequestDataDto.getMaintainEndDate())) {
                        hospitalInfoVO.setMaintainEndDate(dateFormat_day.parse(queryBaseDataRequestDataDto.getMaintainEndDate()));
                    }
                    List<HospitalInfoVO> hospitalInfoList = new ArrayList<>();
                    if (null != queryBaseDataRequestDataDto.getPageIndex() && null != queryBaseDataRequestDataDto.getPageRows()) {
                        Pager pager = new Pager();
                        pager.setPageIndex(queryBaseDataRequestDataDto.getPageIndex());
                        pager.setPageRows(queryBaseDataRequestDataDto.getPageRows());
                        hospitalInfoVO.setPager(pager);
                        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
                        hospitalInfoList = hospitalInfoService.getHospitalInfoList(hospitalInfoVO);
                        PageInfo<HospitalInfoVO> pageInfo = new PageInfo<>(hospitalInfoList);
                        hospitalInfoVO.getPager().setTotalRows((int) pageInfo.getTotal());
                        Map<String, Object> subMap = new HashMap<>();
                        subMap.put("list", hospitalInfoList);
                        subMap.put("pager", hospitalInfoVO.getPager());
                        return ResponseResult.success(subMap);
                    } else {
                        hospitalInfoList = hospitalInfoService.getHospitalInfoList(hospitalInfoVO);
                        if (CollectionUtils.isEmpty(hospitalInfoList) && StringUtils.isNotEmpty(hospitalInfoVO.getHospitalName())) {
                            List<HospitalAliasInfoEntity> aliasInfoEntityList = hospitalAliasInfoService.lambdaQuery()
                                    .eq(HospitalAliasInfoEntity::getDeleteFlag, Boolean.TRUE)
                                    .like(HospitalAliasInfoEntity::getHospitalAlias, hospitalInfoVO.getHospitalName()).list();
                            if(!CollectionUtils.isEmpty(aliasInfoEntityList)){
                                HospitalInfoVO hospitalInfoVOCodeList = new HospitalInfoVO();
                                hospitalInfoVOCodeList.setHospitalCodeList(aliasInfoEntityList.stream().map(HospitalAliasInfoEntity::getHospitalId).collect(Collectors.toList()));
                                hospitalInfoList = hospitalInfoService.getHospitalInfoList(hospitalInfoVOCodeList);
                            }
                        }
                        return ResponseResult.success(hospitalInfoList);
                    }
                } else {
                    return ResponseResult.success(null);
                }
            } else {
                return ResponseResult.fail("999999","请求参数为空");
            }
        } else {
            return ResponseResult.fail("999999","请求参数为空");
        }
    }

    /**
     * 更新医院编码
     * */
    @PostMapping("/updateHospitalCode")
    public void updateHospitalCode() {
        hospitalInfoService.updateHospitalCode();
    }

    /**
     * 获取码值数据
     * */
    @PostMapping("/getGroupUser")
    public ResponseResult<Object> getGroupUser(@RequestBody StandardRequestDTO standardRequestDTO) {
        if (null != standardRequestDTO && null != standardRequestDTO.getRequestData()) {
            String jsonString = JSONObject.toJSONString(standardRequestDTO.getRequestData());
            if (StringUtils.isNotEmpty(jsonString)) {
                LowCodeResquestDTO lowCodeResquestDTO = JSONObject.parseObject(jsonString, LowCodeResquestDTO.class);
                if (null != lowCodeResquestDTO) {
                    String departmentCode = lowCodeResquestDTO.getDepartmentCode();
                    String communicateTitle = lowCodeResquestDTO.getCommunicateTitle();
                    String profitCenter = lowCodeResquestDTO.getProfitCenter();
                    String productCode = lowCodeResquestDTO.getProductCode();
                    String productGroup = lowCodeResquestDTO.getProductGroup();
                    String dispatchCode = configService.getCommunicationConfigByLoop(departmentCode, communicateTitle, profitCenter, productCode, productGroup);
                    return ResponseResult.success(configService.getGroupUser(dispatchCode));
                } else {
                    return ResponseResult.fail("999999","请求参数为空");
                }
            } else {
                return ResponseResult.fail("999999","请求参数为空");
            }
        } else {
            return ResponseResult.fail("999999","请求参数为空");
        }
    }
}
