package com.paic.ncbs.claim.service.openapi.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CollectPayApproachEnum;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.pay.SendPaymentRecordMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.PayFaildInfoDto;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.openapi.FeeInvoiceBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.MergePaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentDTO;
import com.paic.ncbs.claim.model.dto.other.CityDefineDTO;
import com.paic.ncbs.claim.model.dto.pay.PayInfoHead;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.replevy.dao.ClmsRelatedActualReceiptMapper;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.openapi.OpenPayService;
import com.paic.ncbs.claim.service.other.CityDefineService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OpenPayServiceImpl implements OpenPayService {

    @Autowired
    private BpmService bpmService;
    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private CityDefineService cityDefineService;

    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

    @Autowired
    private FeePayMapper feePayMapper;

    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private SendPaymentRecordMapper sendPaymentRecordMapper;
    @Autowired
    private ClmsReplevyChargeMapper clmsReplevyChargeMapper;
    @Autowired
    private ClmsRelatedActualReceiptMapper clmsRelatedActualReceiptMapper;
    @Transactional
    @Override
    public void handlePayBack(List<PaymentBackResultDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "支付结果集合不能为空");
        }
        dtos.forEach(dto -> {
            if (StringUtils.isBlank(dto.getPaySerialNo())) {
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "支付对象唯一ID不能为空");
            }
            if (StringUtils.isBlank(dto.getPayStatus())) {
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "支付状态不能为空");
            }
        });

        dtos.forEach(this::handlePayBack);
    }

    @Transactional
    @Override
    public void updatePayment(PaymentDTO dto) {
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setIdClmPaymentItem(dto.getPaySerialNo());
        List<PaymentItemDTO> paymentItemList = paymentItemMapper.getPaymentItem(paymentItemDTO);
        if (CollectionUtils.isEmpty(paymentItemList) || paymentItemList.size() > 1){
            throw new GlobalBusinessException("支付对象唯一ID错误");
        }
        PaymentItemDTO itemDTO = paymentItemList.get(CommonConstant.ZERO);

        // 挂起支付修改任务
        bpmService.suspendOrActiveTask_oc(itemDTO.getReportNo(), itemDTO.getCaseTimes(), BpmConstants.OC_PAY_BACK_MODIFY, true, itemDTO.getIdClmPaymentItem());

        // 填充非必填数据
        fillModifyInfo(itemDTO, dto);

        //修改：收款方类型、收款人姓名，案件流转至“支付修改审批“池
        if ((StringUtils.isNotBlank(dto.getClientType()) && !dto.getClientType().equals(itemDTO.getClientType()))
                || (StringUtils.isNotBlank(dto.getClientName()) && !dto.getClientName().equals(itemDTO.getClientName()))) {

            //操作记录
            operationRecordService.insertOperationRecord(itemDTO.getReportNo(), BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, "发起", null, ConstValues.SYSTEM_UM);
            String idAhcsTaskInfo =
                    taskInfoMapper.getIdAhcsTaskInfo(itemDTO.getIdClmPaymentItem() +"_REVIEW",
                            null,BpmConstants.OC_PAY_BACK_MODIFY_REVIEW);
            //无支付修改审批任务则创建新的任务
            if(StringUtils.isEmpty(idAhcsTaskInfo)) {
                // 生成支付修改审批任务
                bpmService.startProcessOc(itemDTO.getReportNo(), itemDTO.getCaseTimes(), BpmConstants.OC_PAY_BACK_MODIFY_REVIEW,
                        itemDTO.getIdClmPaymentItem() + "_REVIEW", null, Constants.DEPARTMENT_CODE);
            } else {
                // 打开支付修改审批任务
                bpmService.suspendOrActiveTask_oc(itemDTO.getReportNo(), itemDTO.getCaseTimes(),
                        BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, false, itemDTO.getIdClmPaymentItem() + "_REVIEW");
            }

            String jsonString = JSON.toJSONString(dto);
            itemDTO.setModifyInfo(jsonString);
            paymentItemService.updatePaymentItem(itemDTO);
        } else {
            BeanUtils.copyProperties(dto, itemDTO);
            itemDTO.setProvinceName(dto.getProvinceCode());
            itemDTO.setCityName(dto.getCityCode());
            itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
            paymentItemService.updatePaymentItem(itemDTO);

            // 调用支付接口
            payInfoNoticeThirdPartyCoreSAO.noticePayment(itemDTO.getReportNo(), itemDTO.getCaseTimes(), dto.getPaySerialNo(), false, false);
            //操作记录
            operationRecordService.insertOperationRecord(itemDTO.getReportNo(), BpmConstants.OC_PAY_BACK_MODIFY, "提交", null, ConstValues.SYSTEM_UM);
            bpmService.completeTask_oc(itemDTO.getReportNo(), itemDTO.getCaseTimes(), BpmConstants.OC_PAY_BACK_MODIFY, itemDTO.getIdClmPaymentItem());

        }
    }

    @Override
    public void feeInvoiceBackResult(FeeInvoiceBackResultDTO dto) {
        PaymentItemDTO itemDTO = new PaymentItemDTO();
        itemDTO.setReportNo(dto.getReportNo());
        itemDTO.setIdClmPaymentItem(dto.getPaySerialNo());
        List<PaymentItemDTO> itemDTOS = paymentItemMapper.getAllPaymentItem(itemDTO);
        Integer taskCount=  taskInfoMapper.hasNotFinishTaskByTaskKey(dto.getReportNo(), itemDTOS.get(0).getCaseTimes(), BpmConstants.OC_FEE_INVOICE_MODIFY,dto.getPaySerialNo());
        if (taskCount != 0){
            log.error("有正在处理的发票修改任务,入参:{}",JSON.toJSONString(dto));
            throw new GlobalBusinessException("有正在处理的发票修改任务");
        }
        List<FeeInfoDTO> feeInfoDTOList = this.mergeFeeInfoLists(dto.getPaySerialNo());

        if (CollectionUtils.isEmpty(feeInfoDTOList)){
            log.error("未查到费用信息,入参:{}",JSON.toJSONString(dto));
            return;
        }
        List<String> idAhcsFeePayList = feeInfoDTOList.stream().map(s -> s.getIdAhcsFeePay()).collect(Collectors.toList());
        Integer caseTimes = feeInfoDTOList.get(0).getCaseTimes();

        for (String idAhcsFeePay: idAhcsFeePayList){
            //只查询暂存的发票，待修改的发票不做处理
            InvoiceInfoDTO infoDTO = new InvoiceInfoDTO();
            infoDTO.setInvoiceNo(dto.getInvoiceNo());
            infoDTO.setInvoiceCode(dto.getInvoiceCode());
            infoDTO.setIdAhcsFeePay(idAhcsFeePay);
            infoDTO = feePayMapper.getInvoiceInfo(infoDTO);
            if (Objects.isNull(infoDTO)){
                log.error("收付费调用费用发票退回接口查询不到发票信息,参数:{}",JSON.toJSONString(dto));
                throw new GlobalBusinessException("收付费调用费用发票退回接口查询不到发票信息");
            }
            //更新Invoice表
            Integer count = feePayMapper.updateFeeInvoiceBackResult(dto.getInvoiceCode(),dto.getInvoiceNo(),dto.getReason(),idAhcsFeePay);
            if (count > 0){
                //如同一张发票有待处理或挂起的状态，不产生新任务，发票主键当taskid，后面提交可以一一对应
                addTaskInfo(dto, caseTimes);
            }else {
                log.info("发票无更新数据,入参:{}",JSON.toJSONString(dto));
            }
        }

    }

    @Override
    @Transactional
    public void mergePaymentBackResult(MergePaymentBackResultDTO dto) {
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "打包批次号不能为空");
        }
        if(StringUtils.isBlank(dto.getStatus())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "结算状态不能为空");
        }
        String paymentStatus = "";
        if("11".equals(dto.getStatus())){
            paymentStatus = "80";
        }
        if("12".equals(dto.getStatus()) || "01".equals(dto.getStatus())){
            paymentStatus = "70";
        }
        if(!paymentStatus.isEmpty()){
            paymentItemMapper.updateMergePaymentItemStatus(dto.getBatchNo(),paymentStatus);
            paymentItemMapper.updateMergePaymentStatus(dto.getBatchNo(),paymentStatus);
        }
        paymentItemMapper.updateMergeSettlementStatus(dto.getBatchNo(),dto.getStatus(),dto.getErrorMsg());
    }

    private void addTaskInfo(FeeInvoiceBackResultDTO dto, Integer caseTimes) {

        //判断是否首次退回，非首次打开，将原始任务打开
        TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskKey(dto.getPaySerialNo(),BpmConstants.OC_FEE_INVOICE_MODIFY);
        if (taskInfoDTO!=null) {
            bpmService.suspendOrActiveTask_oc(dto.getReportNo(), caseTimes, BpmConstants.OC_FEE_INVOICE_MODIFY, false, dto.getPaySerialNo());
            //操作记录
            operationRecordService.insertOperationRecord(dto.getReportNo(), BpmConstants.OC_FEE_INVOICE_MODIFY, "重开", null, ConstValues.SYSTEM_UM);
        }else {
            //操作记录
            operationRecordService.insertOperationRecord(dto.getReportNo(), BpmConstants.OC_FEE_INVOICE_MODIFY, "发起", null, WebServletContext.getUserIdForLog());
            // 生成支付修改审批任务
            bpmService.startProcessOc(dto.getReportNo(), caseTimes, BpmConstants.OC_FEE_INVOICE_MODIFY,dto.getPaySerialNo(), null, null);
        }
    }

    /**
     * 接收收付费回调支付结果
     * @param dto 支付结果
     */
    public void handlePayBack(PaymentBackResultDTO dto) {
        log.info("接收支付结果handlePayBack={}", JsonUtils.toJsonString(dto));
        // 对应clm_payment_item 的 主键 ID_CLM_PAYMENT_ITEM
        String paySerialNo = dto.getPaySerialNo();
        // 支付日期
        String paymentDate = dto.getPaymentDate();
        // 1 成功 2 失败
        String payStatus = dto.getPayStatus();
        log.info("接收支付结果handlePayBack支付状态为={}", payStatus);

        // 验证并获取支付项
        PaymentItemDTO paymentItem = validateAndGetPaymentItem(paySerialNo);
        if (paymentItem == null) {
            return;
        }

        // 验证支付项状态是否允许处理
        if (!isPaymentItemStatusValid(paymentItem)) {
            log.info("OpenPayServiceImpl.handlePayBack支付状态不满足处理条件, paySerialNo={}, paymentItemStatus={},查询结果={}"
                    ,paySerialNo, paymentItem.getPaymentItemStatus(),JSONUtil.toJsonStr(paymentItem));
            return;
        }

        String reportNo = paymentItem.getReportNo();
        Integer caseTimes = paymentItem.getCaseTimes();

        if ("1".equals(payStatus)) {
            paymentItem.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_80);
            try {
                paymentItem.setPayDate(DateUtils.parse2Date(paymentDate));
                paymentItem.setArchiveDate(DateUtils.parse2Date(paymentDate));
            } catch (ParseException e) {
                log.error("日期转换异常", e);
            }
        }
        if ("2".equals(payStatus)) {
            paymentItem.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_70);
            try {
                paymentItem.setPayBackDate(DateUtils.parse2Date(paymentDate));
            } catch (ParseException e) {
                log.error("日期转换异常", e);
            }
        }
        if (!"1".equals(payStatus)){
            // 先判断是否是二次退回 如果是则把前一次的支付修改任务重新打开 否则生成支付修改的任务
            TaskInfoDTO taskInfoDTO = taskInfoMapper.selectTaskByTaskKey(paySerialNo,BpmConstants.OC_PAY_BACK_MODIFY);
            String userId = "";
            if (taskInfoDTO!=null){
                bpmService.suspendOrActiveTask_oc(reportNo, caseTimes, BpmConstants.OC_PAY_BACK_MODIFY, false, paySerialNo);
                //操作记录
                operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_PAY_BACK_MODIFY, "重开", null, ConstValues.SYSTEM_UM);
                userId = taskInfoDTO.getAssigner();
            } else {
                // 待支付修改 生成支付修改任务，按照同重开案件类似的逻辑，先判断是否有理算任务，如有直接回到理算岗手里，否则按照保单归属机构递归寻找
                bpmService.startProcess_rc(reportNo, caseTimes, false, BpmConstants.OC_PAY_BACK_MODIFY,paymentItem.getIdClmPaymentItem());
                //操作记录
                operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_PAY_BACK_MODIFY, "发起", null, ConstValues.SYSTEM_UM);
                TaskInfoDTO taskInfoDTO1 = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes,
                        BpmConstants.OC_MANUAL_SETTLE);
                if (taskInfoDTO1 != null){
                    userId = taskInfoDTO1.getAssigner();
                }
            }
            //支付失败时添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_PAY_FALL);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setCaseTimes(caseTimes);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticeService.saveNotices(noticesDTO,dto.getPayee(),userId);
        }
        paymentItem.setExtendInfo(dto.getPayInfo());
        paymentItem.setUpdatedDate(new Date());
        paymentItemMapper.updatePaymentItem(paymentItem);

        // 支付失败 通知渠道 只有赔款的才需要通知渠道
        if ("2".equals(payStatus) && (PaymentTypeEnum.PAY.getType().equals(paymentItem.getPaymentType()) || PaymentTypeEnum.PRE_PAY.getType().equals(paymentItem.getPaymentType()) )) {
            PayFaildInfoDto payFaildInfo = new PayFaildInfoDto();
            BeanUtils.copyProperties(paymentItem, payFaildInfo);
            payFaildInfo.setProvinceCode(paymentItem.getProvinceName());
            payFaildInfo.setCityCode(paymentItem.getCityName());
            payFaildInfo.setPaySerialNo(dto.getPaySerialNo());
            payFaildInfo.setPayInfo(dto.getPayInfo());
            payFaildInfo.setPaymentDate(dto.getPaymentDate());
            payFaildInfo.setPayAmount(dto.getPayAmount());
            payFaildInfo.setPayType(dto.getPayType());

            SyncCaseStatusDto syncCaseStatusDto = new SyncCaseStatusDto();
            syncCaseStatusDto.setReportNo(reportNo);
            syncCaseStatusDto.setCaseTimes(caseTimes);
            syncCaseStatusDto.setCaseStatus(SyncCaseStatusEnum.PAYFAILD);
            syncCaseStatusDto.setPayFaildInfo(payFaildInfo);
            mqProducerSyncCaseStatusService.syncCaseStatus(syncCaseStatusDto);
            return;
        }

        // 根据报案号 判断报案号下的赔款 是否都已经支付成功 如果是 通知 渠道
        int noPay = paymentItemMapper.getNoPayItemByReportNo(reportNo,caseTimes);
        if (noPay == 0){
            SyncCaseStatusDto syncCaseStatusDto = new SyncCaseStatusDto();
            syncCaseStatusDto.setReportNo(reportNo);
            syncCaseStatusDto.setCaseTimes(caseTimes);
            syncCaseStatusDto.setCaseStatus(SyncCaseStatusEnum.PAY);
            mqProducerSyncCaseStatusService.syncCaseStatus(syncCaseStatusDto);
        }
    }

    /**
     * 验证并获取支付项
     */
    private PaymentItemDTO validateAndGetPaymentItem(String paySerialNo) {
        PaymentItemDTO queryParam = new PaymentItemDTO();
        queryParam.setIdClmPaymentItem(paySerialNo);// 对应clm_payment_item 的 主键 ID_CLM_PAYMENT_ITEM

        List<PaymentItemDTO> paymentItemList = paymentItemMapper.getAllPaymentItem(queryParam);

        if (CollectionUtils.isEmpty(paymentItemList)) {
            log.error("OpenPayServiceImpl.handlePayBack查询item数据异常,未查询到支付项数据, paySerialNo={}", paySerialNo);
            return null;
        }

        if (paymentItemList.size() > 1) {
            log.error("OpenPayServiceImpl.handlePayBack查询item数据异常,查询到多条支付项数据, paySerialNo={}, 查询结果:{}",
                    paySerialNo, JSONUtil.toJsonStr(paymentItemList));
            return null;
        }

        return paymentItemList.get(0);
    }

    /**
     * 检查支付项状态是否允许处理
     */
    private boolean isPaymentItemStatusValid(PaymentItemDTO paymentItem) {
        // 状态为11的直接允许处理
        if (Constants.PAYMENT_ITEM_STATUS_11.equals(paymentItem.getPaymentItemStatus())) {
            return true;
        }

        // 状态为80时需要检查支付方式和类型
        if (Constants.PAYMENT_ITEM_STATUS_80.equals(paymentItem.getPaymentItemStatus())) {
            return isRealTimePaymentWithValidType(paymentItem);
        }

        return false;
    }

    /**
     * 检查是否为实时支付且类型有效
     */
    private boolean isRealTimePaymentWithValidType(PaymentItemDTO paymentItem) {
        boolean isRealTimePayment = CollectPayApproachEnum.REAL_TIME_PAYMENT.getType()
                .equals(paymentItem.getCollectPayApproach()); // 实时支付

        if (!isRealTimePayment) {
            return false;
        }

        String paymentType = paymentItem.getPaymentType();
        // 费用、赔款、预赔费用、预赔赔款、追偿费用
        return PaymentTypeEnum.PAY.getType().equals(paymentType) ||
                PaymentTypeEnum.FEE.getType().equals(paymentType) ||
                PaymentTypeEnum.PRE_FEE.getType().equals(paymentType) ||
                PaymentTypeEnum.PRE_PAY.getType().equals(paymentType)||
                PaymentTypeEnum.REPLEVY_FEE.getType().equals(paymentType);
    }


    /**
     * 填充支付修改非必填信息
     * @param itemDTO
     * @param dto
     */
    private void fillModifyInfo(PaymentItemDTO itemDTO, PaymentDTO dto) {
        if (StringUtils.isEmpty(dto.getCityCode())){
            dto.setCityCode(BaseConstant.OTHER_CITY_CODE);
            dto.setRegionCode(BaseConstant.OTHER_COUNTRY_CODE);
        }
        if (StringUtils.isBlank(dto.getRegionCode())){
            List<CityDefineDTO> countys = cityDefineService.getCityDefineDTOList(dto.getCityCode(), ConstValues.CITY_TYPE_CITY);
            dto.setRegionCode(Optional.ofNullable(countys).orElse(Lists.newArrayList()).get(CommonConstant.ZERO).getCityCode());
        }
        if (StringUtils.isBlank(dto.getBankAccountAttribute())) {
            dto.setBankAccountAttribute(itemDTO.getBankAccountAttribute());
        }
        if (StringUtils.isBlank(dto.getBankDetail())) {
            dto.setBankDetail(itemDTO.getBankDetail());
        }
        if (StringUtils.isBlank(dto.getClientType())) {
            dto.setClientType(itemDTO.getClientType());
        }
        if (StringUtils.isBlank(dto.getClientMobile())) {
            dto.setClientMobile(itemDTO.getClientMobile());
        }
        if (StringUtils.isBlank(dto.getCollectPayApproach())) {
            dto.setCollectPayApproach(itemDTO.getCollectPayApproach());
        }
    }

    /**
     * @param paySerialNo
     * @return 合并后的FeeInfoDTO列表
     */
    @Override
    public List<FeeInfoDTO> mergeFeeInfoLists(String paySerialNo) {
        List<FeeInfoDTO> mergedList = new ArrayList<>();
        List<FeeInfoDTO> feeInfoDTOList = feePayMapper.getInvoiceInfoByPaySerialNo(paySerialNo);
        if (feeInfoDTOList != null) {
            mergedList.addAll(feeInfoDTOList);
        }
        List<FeeInfoDTO> replevyFeeInfoDTOList = clmsReplevyChargeMapper.getFeeInfoByIdClmPaymentItem(paySerialNo);
        if (replevyFeeInfoDTOList != null) {
            mergedList.addAll(replevyFeeInfoDTOList);
        }
        return mergedList;
    }

    @Override
    public void otherFeeSettlementResult(MergePaymentBackResultDTO dto) throws GlobalBusinessException {
        SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
        sendPaymentRecord.setReportNo(dto.getBatchNo());
        sendPaymentRecord.setRemark("");
        sendPaymentRecord.setCaseTimes(1);
        sendPaymentRecord.setPaySerialNo("");
        sendPaymentRecord.setRequestType(PayInfoHead.TYPE_N01);
        sendPaymentRecord.setRequestTypeDesc("其他费用结算通知");
        sendPaymentRecord.setRequestParam(JSON.toJSONString(dto));
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "打包批次号不能为空");
        }
        if(StringUtils.isBlank(dto.getStatus())){
            sendPaymentRecord.setResponseParam("结算状态不能为空");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "结算状态不能为空");
        }
        String paymentStatus = "";
        if("02".equals(dto.getStatus())){//核销成功
            paymentStatus = Constants.PAYMENT_ITEM_STATUS_31;
            sendPaymentRecord.setResponseParam("成功");
            sendPaymentRecord.setIsSuccess(ConstValues.YES);
        }
        if("01".equals(dto.getStatus())){//核销失败
            paymentStatus = Constants.PAYMENT_ITEM_STATUS_32;
            sendPaymentRecord.setResponseParam("失败");
            sendPaymentRecord.setIsSuccess(ConstValues.NO);
        }
        if(!paymentStatus.isEmpty()){
            paymentItemMapper.updatePaymentItemStatusByBatchNo(dto.getBatchNo(),paymentStatus);
            clmsRelatedActualReceiptMapper.updateFreezeFlagByBatchNo(dto.getBatchNo(), paymentStatus);
        }
        sendPaymentRecordMapper.insertSelective(sendPaymentRecord);
        log.info("其他费用结算结果处理完成");
    }
}
