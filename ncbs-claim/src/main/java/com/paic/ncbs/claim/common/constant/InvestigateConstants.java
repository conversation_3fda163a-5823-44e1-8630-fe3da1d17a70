package com.paic.ncbs.claim.common.constant;

public class InvestigateConstants {

	private InvestigateConstants() {
		throw new IllegalStateException("Utility class");
	}
	

	public static final String VALIDATE_FLAG_YES = "Y";

	public static final String VALIDATE_FLAG_NO = "N";

	public static final Integer AHCS_INVESTIGATE_STATUS_PROCESSING = 2;

	public static final Integer AHCS_INVESTIGATE_STATUS_AUDIT = 3;

	public static final Integer AHCS_INVESTIGATE_STATUS_FINISH = 4;

	public static final Integer AHCS_INVESTIGATE_STATUS_BACK = 5;

	public static final Integer AHCS_INVESTIGATE_STATUS_EVALUATE = 6;

	public static final String AHCS_INVESTIGATE_TASK_STATUS_PROCESSING = "1";

	public static final String AHCS_INVESTIGATE_AUDIT_TYPE_OFFSITE = "01";

	public static final String ID_AHCS_INVESTIGATE = "idAhcsInvestigate";
	public static final String DEPARTMENT_CODE = "departmentCode";
	public static final String INVESTIGATOR_UM = "investigatorUm";

	public static final String ID_AHCS_INVESTIGATE_TASK = "idAhcsInvestigateTask";
	public static final String IS_OLD_INVESTIGATE = "isOldInvestigate";
	public static final String DISPATCH_UM = "dispatchUm";

	public static final String INVESTIGATE_AUDIT_OPINION_REJECT = "调查退回";
	public static final String INVESTIGATE_AUDIT_OPINION_TRANSFER = "移交审批";

	public static final String AHCS_INVESTIGATE_INIT_MODE_AUTO = "01";

	/**
	 * 打印类型 1:调查 2:委托
	 */
	public static final String PRINT_INVESTIGATE = "1";
	/**
	 * 打印类型 1:调查 2:委托
	 */
	public static final String PRINT_ENTRUSTMENT = "2";






}