package com.paic.ncbs.claim.service.rule.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.enums.CaseRiskTypeEnums;
import com.paic.ncbs.claim.common.enums.RuleTypeEnums;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.standard.bill.enums.BillEnum;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity;
import com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDiagnoseMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.mistake.MistakeRecordMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DiagnoseHospitalBillAssociationMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.ClaimRuleResultDTO;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.RuleSubResultDTO;
import com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.rule.*;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.settle.CompareBillNoResultVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import com.paic.ncbs.claim.service.common.ClmsEveryMonthPayTimesCheckService;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyAllInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.duty.DutyAttrAsyncInitService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.risk.RiskCheckService;
import com.paic.ncbs.claim.service.risk.RiskControlService;
import com.paic.ncbs.claim.service.rule.AutoRuleRecordService;
import com.paic.ncbs.claim.service.rule.AutoRuleService;
import com.paic.ncbs.claim.service.rule.RuleService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 自动规则服务
 * @date 2024/03/28
 */
@Service
public class AutoRuleServiceImpl implements AutoRuleService {

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private FeePayService feePayService;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private MedicalBillService medicalBillService;
    @Autowired
    private ResidueAmountService residueAmountService;
    @Autowired
    private ClmsPolicySurrenderInfoService clmsPolicySurrenderInfoService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private MistakeRecordMapper mistakeRecordMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private DiagnoseHospitalBillAssociationMapper diagnoseHospitalBillAssociationMapper;
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;
    @Autowired
    private RuleService ruleService;
    @Autowired
    private AutoRuleRecordService autoRuleRecordService;
    @Autowired
    private ClmsEveryMonthPayTimesCheckService clmsEveryMonthPayTimesCheckService;
    @Autowired
    private RiskCheckService riskCheckService;
    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;
    @Autowired
    private ClmsQueryPolicyAllInfoService clmsQueryPolicyAllInfoService;
    @Autowired
    private ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;
    @Autowired
    private AhcsPolicyPlanMapper ahcsPolicyPlanMapper;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;
    @Autowired
    private DutyAttrAsyncInitService dutyAttrAsyncInitService;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Autowired
    private PersonDiagnoseMapper personDiagnoseMapper;

    @Value("${switch.rule:false}")
    private Boolean switchRule;
    @Value("${project.monthProjectCode:N}")
    private String configMonthProjectCode;
    @Value("${product.C02P00008:02P00047}")
    private String C02P00008;


    /**
     * 执行核责规则服务
     *
     * @param reportNo
     * @param caseTimes
     * @param copyPolicyPays
     * @return
     */
    @Override
    public void execDutyConfirmRule(String reportNo, Integer caseTimes, List<PolicyPayDTO> copyPolicyPays) {
        LogUtil.info("execDutyConfirmRule核责调用开始：{},{}", reportNo, caseTimes);
        // 初始化返回对象
        if (!switchRule) {
            LogUtil.info("execDutyConfirmRule核责规则开关未开启！");
            return;
        }

        CaseInfoBO caseInfoBO = getBaseCaseInfoBO(reportNo,caseTimes);

        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        try {
            for (PolicyPayDTO policyPayDTO : copyPolicyPays) {
                Optional<PolicyInfoDTO> policyInfoOpts =
                        policyInfoDTOList.stream().filter(item -> item.getPolicyNo().equals(policyPayDTO.getPolicyNo())).findFirst();
                Optional<OcasPolicyPlanDutyDTO> ocasPolicyInfoOpts =
                        Optional.ofNullable(ocasMapper.getPolicyInfoByPolicyNo(policyPayDTO.getPolicyNo()));
                Date insuranceBeginTime = policyPayDTO.getInsuranceBeginTime();
                Date insuranceEndTime = policyPayDTO.getInsuranceEndTime();
                if (ocasPolicyInfoOpts.isPresent()) {
                    insuranceBeginTime = ocasPolicyInfoOpts.get().getInsuranceBeginDate();
                    insuranceEndTime = ocasPolicyInfoOpts.get().getInsuranceEndDate();
                }
                PolicyInfoBO policyInfoBO = new PolicyInfoBO();
                policyInfoBO.setPolicyNo(policyPayDTO.getPolicyNo());
                policyInfoBO.setProductCode(policyPayDTO.getProductCode());
                policyInfoBO.setInsuranceBeginDate(DateUtils.parseToDate(DateUtils.dateFormat(insuranceBeginTime,
                        DateUtils.SIMPLE_DATE_STR)));
                policyInfoBO.setInsuranceEndDate(DateUtils.parseToDate(DateUtils.dateFormat(insuranceEndTime,
                        DateUtils.SIMPLE_DATE_STR)));
                policyInfoBO.setInsuranceBeginTime(insuranceBeginTime);
                policyInfoBO.setInsuranceEndTime(insuranceEndTime);
                policyInfoOpts.ifPresent(policyInfoDTO -> {
                    policyInfoBO.setPackageCode(policyInfoDTO.getProductPackageType());
                    policyInfoBO.setPackageName(policyInfoDTO.getProductPackageName());
                });
                // 查询被保险人姓名
                ReportCustomerInfoEntity reportCustomerInfoEntity =
                        reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
                policyInfoBO.setInsuredName(reportCustomerInfoEntity.getName());
                policyInfoBO.setInsuredGender(reportCustomerInfoEntity.getSexCode());
                getAge(reportCustomerInfoEntity, policyInfoBO.getInsuranceBeginDate());
                policyInfoBO.setInsuredAge(reportCustomerInfoEntity.getAge());
                policyInfoBO.setInsuredCertificateType(reportCustomerInfoEntity.getCertificateType());
                policyInfoBO.setInsuredCertificateNo(reportCustomerInfoEntity.getCertificateNo());
                policyInfoBO.setInsuredClientCluster(reportCustomerInfoEntity.getClientCluster());

                caseInfoBO.setPolicyNo(policyInfoBO.getPolicyNo());
                caseInfoBO.setProductCode(policyInfoBO.getProductCode());
                caseInfoBO.setInsuranceBeginDate(policyInfoBO.getInsuranceBeginDate());
                caseInfoBO.setInsuranceEndDate(policyInfoBO.getInsuranceEndDate());
                caseInfoBO.setPackageCode(policyInfoBO.getPackageCode());
                caseInfoBO.setPackageName(policyInfoBO.getPackageName());


                List<DutyDetailInfoBO> dutyDetailInfoBOList = new ArrayList<>();
                for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                    for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                        for (DutyDetailPayDTO dutyDetailPayDTO : dutyPayDTO.getDutyDetailPayArr()) {
                            DutyDetailInfoBO dutyDetailInfoBO = new DutyDetailInfoBO();
                            dutyDetailInfoBO.setPolicyNo(policyInfoBO.getPolicyNo());
                            dutyDetailInfoBO.setProductCode(policyInfoBO.getProductCode());
                            dutyDetailInfoBO.setProductClass(policyPayDTO.getProductClass());
                            dutyDetailInfoBO.setPackageCode(policyInfoBO.getPackageCode());
                            dutyDetailInfoBO.setPlanCode(planPayDTO.getPlanCode());
                            dutyDetailInfoBO.setPlanName(planPayDTO.getPlanName());
                            dutyDetailInfoBO.setDutyCode(dutyPayDTO.getDutyCode());
                            dutyDetailInfoBO.setDutyName(dutyPayDTO.getDutyName());
                            dutyDetailInfoBO.setDutyDetailCode(dutyDetailPayDTO.getDutyDetailCode());
                            dutyDetailInfoBO.setDutyDetailName(dutyDetailPayDTO.getDutyDetailName());
                            dutyDetailInfoBO.setDutyDetailType(dutyDetailPayDTO.getDutyDetailType());
                            dutyDetailInfoBO.setInsuranceBeginDate(dutyDetailPayDTO.getInsuranceBeginTime());
                            dutyDetailInfoBO.setInsuranceEndDate(dutyDetailPayDTO.getInsuranceEndTime());
                            dutyDetailInfoBO.setIsSettleFlag("N");
                            dutyDetailInfoBOList.add(dutyDetailInfoBO);
                        }
                    }
                }
                policyInfoBO.setDutyDetailInfoBOList(dutyDetailInfoBOList);

                AutoRuleParam dutyConfirmParam = new AutoRuleParam();
                dutyConfirmParam.setPolicyInfoBO(policyInfoBO);
                dutyConfirmParam.setCaseInfoBO(caseInfoBO);
                dutyConfirmParam.setBillInfoBO(null);
                RuleResultDTO ruleResultDTO = ruleService.dutyConfirmRule(dutyConfirmParam);
                // 更新封装返回对象
                if (ruleResultDTO != null) {
                    LogUtil.info("核责规则结果:{}", JSON.toJSON(ruleResultDTO));
                    if (ruleResultDTO.getData() != null){
                        List<DutyDetailInfoBO> dutyDetailInfoBOS = (List<DutyDetailInfoBO>) ruleResultDTO.getData();
                        for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                            for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                                for (DutyDetailPayDTO dutyDetailPayDTO : dutyPayDTO.getDutyDetailPayArr()) {
                                    Optional<DutyDetailInfoBO> detailInfoOpt =
                                            dutyDetailInfoBOS.stream().filter(item -> StringUtils.equals(item.getDutyDetailCode(), dutyDetailPayDTO.getDutyDetailCode()) && StringUtils.equals(item.getDutyCode(), dutyPayDTO.getDutyCode())).findFirst();
                                    String isSettleFlag = "N";
                                    if (detailInfoOpt.isPresent()) {
                                        isSettleFlag = detailInfoOpt.get().getIsSettleFlag();
                                    }
                                    LogUtil.info("核责规则结果:{}:{}=={}", dutyPayDTO.getDutyCode(),
                                            dutyDetailPayDTO.getDutyDetailCode(), isSettleFlag);
                                    dutyDetailPayDTO.setIsSettleFlag(isSettleFlag);
                                }
                            }
                        }

                    }

                }
            }
        } catch (Exception e) {
            LogUtil.error("execDutyConfirmRule核责规则结果异常结束:", e);
        }
    }

    /**
     * 执行理算服务
     *
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public ClaimRuleResultDTO executeSettleRule(String reportNo, Integer caseTimes) {
        LogUtil.info("executeSettleRule自核调用开启：{}",reportNo,caseTimes);
        // 初始化返回对象
        ClaimRuleResultDTO  claimRuleResultDTO = new ClaimRuleResultDTO();
        claimRuleResultDTO.setReportNo(reportNo);
        claimRuleResultDTO.setCaseTimes(caseTimes);
        claimRuleResultDTO.setAutoPass(false);
        claimRuleResultDTO.setRuleType(BpmConstants.OC_MANUAL_SETTLE);
        claimRuleResultDTO.setAutoZeroCancel(false);
        claimRuleResultDTO.setRuleSubResultList(null);

        if(!switchRule){
            LogUtil.info("executeSettleRule自核规则开关未开启！");
            return claimRuleResultDTO;
        }


        CaseInfoBO caseInfoBO = getBaseCaseInfoBO(reportNo,caseTimes);
        // 是否存在重复账单信息
        caseInfoBO.setCbitSameBillInfo(riskControlService.getCBITSameInfo(reportNo, caseTimes));
        Map<String, CompareBillNoResultVO> resultVOMap = medicalBillService.checkAllBillDuplicate(reportNo, caseTimes);
        caseInfoBO.setIsExistingDuplicateBillFlag(MapUtils.isNotEmpty(resultVOMap));

        BillInfoListBO billInfoListBO = new BillInfoListBO();
        List<BillInfoBO>  caseSameBillList = new ArrayList<>();
        List<BillInfoBO>  otherCaseSameBillList = new ArrayList<>();
        List<BillInfoBO>  billInfoListBOList = new ArrayList<>();
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        for (MedicalBillInfoVO medicalBillInfoVO : Optional.ofNullable(medicalBillInfoVOS).orElseGet(Collections::emptyList)) {
            BillInfoBO billInfoBO = new BillInfoBO();
            // 处理当天投保当天报案，发票日为当天问题
            billInfoBO.setBillDate(DateUtils.endOfDay(medicalBillInfoVO.getStartDate()));
            billInfoBO.setBillNo(medicalBillInfoVO.getBillNo());
            billInfoBO.setBillType(medicalBillInfoVO.getBillType());
            billInfoBO.setTherapyType(medicalBillInfoVO.getTherapyType());
            billInfoBO.setPayer(medicalBillInfoVO.getPayer());
            billInfoBO.setVisitorGender(medicalBillInfoVO.getGender());
            billInfoBO.setStartDate(medicalBillInfoVO.getStartDate());
            billInfoBO.setEndDate(medicalBillInfoVO.getEndDate());
            billInfoBO.setHospitalProperty(medicalBillInfoVO.getHospitalPropertyDes());
            billInfoBO.setHospitalGrade(medicalBillInfoVO.getGrade());
            billInfoBO.setHospitalName(medicalBillInfoVO.getHospitalName());

            //增加诊断信息，从clms_diagnose_hospital_bill_association表获取 可能多条
            List<DiagnoseHospitalBillAssociationDTO> list =
                    diagnoseHospitalBillAssociationMapper.getDiagnoseHospitalBillAssociation(medicalBillInfoVO.getIdAhcsBillInfo());
            List<DiagnoseInfoBO> diagnoseInfoBOList = list.stream().map(item -> {
                DiagnoseInfoBO diagnoseInfoBO = new DiagnoseInfoBO();
                diagnoseInfoBO.setTypologyCode(item.getDiagnosticTypologyCode());
                diagnoseInfoBO.setDiagnoseCode(item.getDiagnoseCode());
                diagnoseInfoBO.setDiagnoseName(item.getDiagnoseName());
                return diagnoseInfoBO;
            }).collect(Collectors.toList());
            billInfoBO.setDiagnoseInfoList(diagnoseInfoBOList);
            // select S.Cost_Comment,s.* from CLMS_bill_detail s;
            billInfoBO.setRiskWarningInfoList(null);
            List<MedicalBillDetailDTO> medicalBillDetailDTOList = medicalBillInfoVO.getMedicalBillDetailDTOList();
            if (CollectionUtils.isNotEmpty(medicalBillDetailDTOList)) {
                List<String> riskWarningList = medicalBillDetailDTOList.stream().map(item -> {
                    if (StringUtils.isBlank(item.getCostComment())) {
                        return new ArrayList<String>();
                    }
                    return Lists.newArrayList(StringUtils.split(item.getCostComment(),Constants.SEPARATOR));
                }).reduce(new ArrayList<>(), (all, val) -> {
                    all.addAll(val);
                    return all;
                }).stream().distinct().collect(Collectors.toList());
                List<RiskWarningInfoBO> riskWarningInfoBOList = riskWarningList.stream().map(item -> {
                    RiskWarningInfoBO riskWarningInfoBO = new RiskWarningInfoBO();
                    riskWarningInfoBO.setCode(item);
                    riskWarningInfoBO.setDesc(BillEnum.getName(item));
                    return riskWarningInfoBO;
                }).collect(Collectors.toList());
                billInfoBO.setRiskWarningInfoList(riskWarningInfoBOList);
            }

            if(resultVOMap.containsKey(medicalBillInfoVO.getIdAhcsBillInfo())){
                CompareBillNoResultVO compareBillNoResultVO = resultVOMap.get(medicalBillInfoVO.getIdAhcsBillInfo());
                if(compareBillNoResultVO.getCurrentCaseSameBill() != null){
                    caseSameBillList.add(billInfoBO);
                }
                if(CollectionUtils.isNotEmpty(compareBillNoResultVO.getOtherCaseReportNoList())){
                    otherCaseSameBillList.add(billInfoBO);
                }
            }

            billInfoListBOList.add(billInfoBO);
        }
        billInfoListBO.setBillInfoList(billInfoListBOList);
        caseInfoBO.setCaseSameBillList(caseSameBillList);
        caseInfoBO.setOtherCaseSameBillList(otherCaseSameBillList);

        List<PolicyPayDTO>  policyPayDTOS = clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo,caseTimes);
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        try {
            for (PolicyPayDTO policyPayDTO : policyPayDTOS) {
                Optional<PolicyInfoDTO> policyInfoOpts =
                        policyInfoDTOList.stream().filter(item -> item.getPolicyNo().equals(policyPayDTO.getPolicyNo())).findFirst();
                Optional<OcasPolicyPlanDutyDTO> ocasPolicyInfoOpts =
                        Optional.ofNullable(ocasMapper.getPolicyInfoByPolicyNo(policyPayDTO.getPolicyNo()));
                Date insuranceBeginTime = policyPayDTO.getInsuranceBeginTime();
                Date insuranceEndTime = policyPayDTO.getInsuranceEndTime();
                if (ocasPolicyInfoOpts.isPresent()) {
                    insuranceBeginTime = ocasPolicyInfoOpts.get().getInsuranceBeginDate();
                    insuranceEndTime = ocasPolicyInfoOpts.get().getInsuranceEndDate();
                }
                PolicyInfoBO policyInfoBO = new PolicyInfoBO();
                policyInfoBO.setPolicyNo(policyPayDTO.getPolicyNo());
                policyInfoBO.setProductCode(policyPayDTO.getProductCode());
                policyInfoBO.setInsuranceBeginDate(DateUtils.parseToDate(DateUtils.dateFormat(insuranceBeginTime,
                        DateUtils.SIMPLE_DATE_STR)));
                policyInfoBO.setInsuranceEndDate(DateUtils.parseToDate(DateUtils.dateFormat(insuranceEndTime,
                        DateUtils.SIMPLE_DATE_STR)));
                policyInfoBO.setInsuranceBeginTime(insuranceBeginTime);
                policyInfoBO.setInsuranceEndTime(insuranceEndTime);
                policyInfoOpts.ifPresent(policyInfoDTO -> {
                    policyInfoBO.setPackageCode(policyInfoDTO.getProductPackageType());
                    policyInfoBO.setPackageName(policyInfoDTO.getProductPackageName());
                });
                //查询被保险人姓名
                ReportCustomerInfoEntity reportCustomerInfoEntity =
                        reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
                policyInfoBO.setInsuredName(reportCustomerInfoEntity.getName());
                policyInfoBO.setInsuredGender(reportCustomerInfoEntity.getSexCode());
                getAge(reportCustomerInfoEntity,policyInfoBO.getInsuranceBeginDate());
                policyInfoBO.setInsuredAge(reportCustomerInfoEntity.getAge());
                policyInfoBO.setInsuredCertificateType(reportCustomerInfoEntity.getCertificateType());
                policyInfoBO.setInsuredCertificateNo(reportCustomerInfoEntity.getCertificateNo());
                policyInfoBO.setInsuredClientCluster(reportCustomerInfoEntity.getClientCluster());

                caseInfoBO.setPolicyNo(policyInfoBO.getPolicyNo());
                caseInfoBO.setProductCode(policyInfoBO.getProductCode());
                caseInfoBO.setInsuranceBeginDate(policyInfoBO.getInsuranceBeginDate());
                caseInfoBO.setInsuranceEndDate(policyInfoBO.getInsuranceEndDate());
                caseInfoBO.setPackageCode(policyInfoBO.getPackageCode());
                caseInfoBO.setPackageName(policyInfoBO.getPackageName());

                AutoSettleParameter autoSettleParameter = new AutoSettleParameter();
                autoSettleParameter.setPolicyInfoBO(policyInfoBO);
                autoSettleParameter.setCaseInfoBO(caseInfoBO);
                autoSettleParameter.setBillInfoBO(billInfoListBO);
                RuleResultDTO ruleResultDTO = ruleService.autoSettleRule(autoSettleParameter);
                // 更新封装返回对象
                claimRuleResultDTO.updateRuleResult(ruleResultDTO);
            }
        } catch (Exception e) {
            claimRuleResultDTO.setAutoPass(false);
            LogUtil.error("executeSettleRule自核规则结果异常结束:",e);
        }

        // 扩展理算规则 extendSettleRule
        extendSettleRule(claimRuleResultDTO);
        // 扩展零注规则 extendZeroCancelRule
        extendCancelRule(policyPayDTOS,claimRuleResultDTO);

        LogUtil.info("executeSettleRule调用结束，返回结果: {}", claimRuleResultDTO);
        // 保存数据记录
        autoRuleRecordService.saveRuleRecord(claimRuleResultDTO);
        return claimRuleResultDTO;
    }



    /**
     * 扩展理赔规则执行
     * @param claimRuleResultDTO
     */
    private void extendSettleRule(ClaimRuleResultDTO  claimRuleResultDTO){
        if(!claimRuleResultDTO.isAutoPass()){
            // 如果已经不是自动理算了，则不执行逻辑
            return;
        }
        String reportNo = claimRuleResultDTO.getReportNo();
        Integer caseTimes = claimRuleResultDTO.getCaseTimes();

        /*
         * 补充规则1：检查案件被保险人公司内理赔风险：1、其它案件理赔时调查异常；2、其它案件理赔时存在二核拒保。
         */
        addCustomerClaimRisk(reportNo,claimRuleResultDTO);

        /*
         * 补充规则2：其他案件未完成调查任务或者二核任务
         */
        if(claimRuleResultDTO.isAutoPass()){
           addOtherCaseUnfinishedTask(reportNo,caseTimes,claimRuleResultDTO);
        }
    }

    /**
     * 扩展零注(注销)规则执行
     * @param policyPayDTOS
     * @param claimRuleResultDTO
     */
    private void extendCancelRule(List<PolicyPayDTO>  policyPayDTOS, ClaimRuleResultDTO  claimRuleResultDTO){
        if(claimRuleResultDTO.isAutoZeroCancel()){
            // 如果已经满足自动零注的话，则不执行逻辑
            return;
        }
        String reportNo = claimRuleResultDTO.getReportNo();
        Integer caseTimes = claimRuleResultDTO.getCaseTimes();

        /*
         * 补充规则1：长鹅门诊赠险超每月赔付天数自动发起零注
         * 还要分理算规则通过，或者理算规则不存在两个方向要处理一下。
         */
        addCancelRuleMonthPayDays(reportNo,caseTimes,policyPayDTOS,claimRuleResultDTO);


    }

    /**
     * 添加零注(注销)规则：每月赔付天数
     *  赠险P02P00008001、P02P00008002时自核提示“超索赔天数”
     * @param reportNo
     * @param caseTimes
     */
    private void addCancelRuleMonthPayDays(String reportNo,Integer caseTimes,List<PolicyPayDTO>  policyPayDTOS, ClaimRuleResultDTO  claimRuleResultDTO){
        // 先不判断产品或者方案。
        List<DutyAttributeValueDTO> dutyAttributeValueDTOList = dutyAttributeMapper.getDutyAttributePayDays(reportNo);
        if (CollectionUtils.isEmpty(dutyAttributeValueDTOList)) {
            LogUtil.info("案件：{}没有每月赔付天数615属性！", reportNo);
            return;
        }
        // 查询本责任赔付的账单信息：理算之前查询账单信息
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(medicalBillInfoVOS)) {
            LogUtil.info("案件：{}没有账单信息暂不注销！", reportNo);
            return;
        }
        // 过滤出 门诊发票
        List<MedicalBillInfoVO> medicalBillInfoVOList =
                medicalBillInfoVOS.stream().filter(item -> StringUtils.equals(item.getTherapyType(),
                        ChecklossConst.AHCS_THERAPY_ONE)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(medicalBillInfoVOList)) {
            LogUtil.info("案件：{}没有门诊账单信息暂不注销！", reportNo);
            return;
        }
        // 27092长鹅门诊赠险超每月赔付天数自动发起零注
        boolean isAllCannotPaid = false;
        for (PolicyPayDTO policyPayDTO : policyPayDTOS) {
            // 产品代码
            String productCode = policyPayDTO.getProductCode();
            if(!StringUtils.equals(productCode, C02P00008)){
                LogUtil.info("案件：{}非长鹅门诊赠险！", reportNo);
                continue;
            }
            // 得到保单的起止日期
            PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyPayDTO.getPolicyNo());
            // 根据起止日期计算月数
            List<PolicyMonthDto> monthDtoList =
                    clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(), policyDto.getPolicyEndDate());
            for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                    // 查询是否有每月赔付天数属性
                    List<DutyAttributeDTO> attributeList =
                            dutyAttrAsyncInitService.getOtherDutyAttributeList(dutyPayDTO.getIdCopyDuty());
                    Optional<DutyAttributeDTO> attributeDTOOpt =
                            attributeList.stream().filter(item -> DutyAttributeConst.EVERY_DAY_PAY_DAYS.equals(item.getAttrCode())).findFirst();
                    if (!attributeDTOOpt.isPresent()) {
                        continue;
                    }
                    //每月赔付天数
                    int payDays = Integer.parseInt(attributeDTOOpt.get().getAttrValue());
                    for (PolicyMonthDto policyMonthDto : monthDtoList) {
                        List<Date> billDateList = new ArrayList<>();
                        for (MedicalBillInfoVO medicalBillInfoDTO : medicalBillInfoVOList) {
                            Date billDate =medicalBillInfoDTO.getStartDate();
                            if (null != billDate &&
                                    billDate.compareTo(policyMonthDto.getStartDate()) >= 0 &&
                                    billDate.compareTo(policyMonthDto.getEndDate()) <= 0 &&
                                    !billDateList.contains(billDate)) {
                                billDateList.add(billDate);
                            }
                        }
                        if (CollectionUtils.isEmpty(billDateList)){
                            // 当月没有赔付账单
                            continue;
                        }
                        // 查询保单责任历史赔付天数
                        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
                        dutyLimitQueryVo.setPolicyNo(policyPayDTO.getPolicyNo());
                        dutyLimitQueryVo.setPlanCode(planPayDTO.getPlanCode());
                        dutyLimitQueryVo.setDutyCode(dutyPayDTO.getDutyCode());
                        dutyLimitQueryVo.setSatrtDate(policyMonthDto.getStartDate());
                        dutyLimitQueryVo.setEndDate(policyMonthDto.getEndDate());
                        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);

                        if (CollectionUtils.isEmpty(dtos) ) {
                            LogUtil.info("案件：{},无历史赔付发票可以赔付！", reportNo, billDateList);
                            isAllCannotPaid = false;
                            break;
                        }

                        Map<Date, List<DutyBillLimitDto>> mothListMap = dtos.stream()
                                .sorted(Comparator.comparing(DutyBillLimitDto::getBillDate))
                                .collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate, LinkedHashMap::new, Collectors.toList()));
                        List<Date> paidBillDateList = new ArrayList<>();
                        for (Map.Entry<Date, List<DutyBillLimitDto>> entry : mothListMap.entrySet()) {
                            List<DutyBillLimitDto> evDayList = entry.getValue();
                            List<String> caseNoList =
                                    evDayList.stream().map(DutyBillLimitDto::getCaseNo).collect(Collectors.toList());
                            Integer indmenityCount = dutyDetailPayMapper.getIndmenityInfo(caseNoList,
                                    dutyPayDTO.getDutyCode());
                            if (indmenityCount >= 1) {
                                if(!paidBillDateList.contains(entry.getKey())){
                                    paidBillDateList.add(entry.getKey());
                                }
                            }
                        }
                        // 历史赔付天数已经赔满并且本次赔付日期和历史没有重合，则本次赔付
                        if(paidBillDateList.size()>=payDays && Collections.disjoint(billDateList,paidBillDateList)){
                            LogUtil.info("案件：{},账单日期:{}，无法赔付！", reportNo, billDateList);
                            isAllCannotPaid = true;
                        } else {
                            // 有发票可以赔付
                            isAllCannotPaid = false;
                            break;
                        }

                    }
                }
            }
        }
        // 是否满足账单全部每月赔付次数内。
        if(isAllCannotPaid){
            // 超索赔天数
            claimRuleResultDTO.addCancelExtendRule("310001");
        }
    }

    private void addCustomerClaimRisk(String reportNo, ClaimRuleResultDTO  claimRuleResultDTO){
        // 检查案件被保险人公司内理赔风险：1、其它案件理赔时调查异常；2、其它案件理赔时存在二核拒保。
        String claimRiskMsg = riskCheckService.checkCustomerClaimRisk(reportNo);
        if(StringUtils.isNotBlank(claimRiskMsg)){
            claimRuleResultDTO.setAutoPass(false);
            claimRuleResultDTO.setAutoZeroCancel(false);
            RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
            ruleSubResultDTO.setRuleCode("999999");
            ruleSubResultDTO.setRuleReason("原因：" + claimRiskMsg);
            ruleSubResultDTO.setBillNo("");
            List<RuleSubResultDTO> ruleSubResultList =
                    Optional.ofNullable(claimRuleResultDTO.getRuleSubResultList()).orElseGet(ArrayList::new);
            ruleSubResultList.add(ruleSubResultDTO);
            claimRuleResultDTO.setRuleSubResultList(ruleSubResultList);
        }
    }
    private void addOtherCaseUnfinishedTask(String reportNo,Integer caseTimes,
                                            ClaimRuleResultDTO  claimRuleResultDTO){
        try {
            // 其他案件未完成调查任务或者二核任务
            riskCheckService.checkOtherCaseUnfinishedTask(reportNo,caseTimes);
        } catch (Exception e){
            String message = e.getMessage();
            claimRuleResultDTO.setAutoPass(false);
            claimRuleResultDTO.setAutoZeroCancel(false);
            RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
            ruleSubResultDTO.setRuleCode("999999");
            ruleSubResultDTO.setRuleReason("原因：" + message);
            ruleSubResultDTO.setBillNo("");
            List<RuleSubResultDTO> ruleSubResultList =
                    Optional.ofNullable(claimRuleResultDTO.getRuleSubResultList()).orElseGet(ArrayList::new);
            ruleSubResultList.add(ruleSubResultDTO);
            claimRuleResultDTO.setRuleSubResultList(ruleSubResultList);
        }
    }



    /**
     * 执行理算核赔规则
     * 1.	进行自动核赔校验的案件
     *  当次理算只赔付方案+C00034责任的案件。
     * 2.	自动核赔通过规则
     * 2.1.	当前案件结论为正常赔付
     * 2.2.	80≥赔款金额＞0
     * 2.3.	当前保单已赔付结案且赔付金额＞0的件数＜4
     * 3.	自动核赔不通过规则
     * 3.1.	计算结果不符合日限额、累计保额的校验
     * 3.2.	本案存在理赔费用                                 -- 已查
     * 3.3.	保单起期≤收单录入的所有发票日期≤保单止期
     * 3.4.	本案曾有过【理赔核保】                            -- 已查
     * 3.5.	本案存在“重复发票提醒”
     * 3.6.	本案已录入发票存在重复
     * @description 查询案件列表
     */
    @Override
    public ClaimRuleResultDTO executeVerifySettleRule(String reportNo,Integer caseTimes) {
        ClaimRuleResultDTO  claimRuleResultDTO = new ClaimRuleResultDTO();
        claimRuleResultDTO.setReportNo(reportNo);
        claimRuleResultDTO.setCaseTimes(caseTimes);
        claimRuleResultDTO.setAutoPass(false);
        claimRuleResultDTO.setRuleType(BpmConstants.OC_SETTLE_REVIEW);
        claimRuleResultDTO.setAutoZeroCancel(false);
        claimRuleResultDTO.setRuleSubResultList(Lists.newArrayList());

        if(!switchRule){
            LogUtil.info("executeVerifySettleRule自核规则开关未开启！");
            return claimRuleResultDTO;
        }

        if(!Integer.valueOf(1).equals(caseTimes)){
            LogUtil.info("案件：{}重开不走自动核赔规则！",reportNo);
            return claimRuleResultDTO;
        }

        //查询规则结果，进行自核风险校验
        AutoRuleMainInfoEntity autoRuleMainInfoEntity = autoRuleRecordService.getRuleRecord(reportNo, caseTimes,BpmConstants.OC_MANUAL_SETTLE);
        if(Objects.nonNull(autoRuleMainInfoEntity) && "N".equals(autoRuleMainInfoEntity.getIsRulePass())) {
            List<AutoRuleDetailInfoEntity> autoRuleDetailInfoEntities = autoRuleMainInfoEntity.getAutoRuleDetailInfoEntities();
            for(AutoRuleDetailInfoEntity autoRuleDetail: autoRuleDetailInfoEntities) {
                if(RuleTypeEnums.RULE_TYPE_000005.getCode().equals(autoRuleDetail.getRuleCode()) || RuleTypeEnums.RULE_TYPE_000071.getCode().equals(autoRuleDetail.getRuleCode())) {
                    LogUtil.info("案件{}存在自核风险，风险点：{}",reportNo,autoRuleDetail.getRuleCode());
                    RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                    ruleSubResultDTO.setRuleCode(RuleTypeEnums.RULE_TYPE_899999.getCode());
                    ruleSubResultDTO.setRuleReason("案件理赔规则触发规则：发票日期不在保单有效期内或就诊疾病不属于保险责任编码");
                    ruleSubResultDTO.setBillNo("");
                    claimRuleResultDTO.setAutoPass(false);
                    claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
                    break;
                } else if(RuleTypeEnums.RULE_TYPE_000081.getCode().equals(autoRuleDetail.getRuleCode())
                        && StringUtils.containsAny(autoRuleDetail.getRuleMessage(),BillEnum.RISK_BILL_204.getName(),
                        BillEnum.RISK_BILL_205.getName(),BillEnum.RISK_BILL_209.getName(),
                        BillEnum.RISK_BILL_210.getName(),BillEnum.RISK_BILL_211.getName(),
                        BillEnum.RISK_BILL_212.getName(),BillEnum.RISK_BILL_213.getName())) {
                    LogUtil.info("案件{}存在自核风险，风险点：{},TPA风险：{}",reportNo,autoRuleDetail.getRuleCode(),autoRuleDetail.getRuleMessage());
                    RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                    ruleSubResultDTO.setRuleCode(RuleTypeEnums.RULE_TYPE_899999.getCode());
                    ruleSubResultDTO.setRuleReason("案件理赔规则触发规则：TPA发票风险四大类型！");
                    ruleSubResultDTO.setBillNo("");
                    claimRuleResultDTO.setAutoPass(false);
                    claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
                    break;
                } else if(StringUtils.equalsAny(autoRuleDetail.getRuleCode(),RuleTypeEnums.RULE_TYPE_010001.getCode(),RuleTypeEnums.RULE_TYPE_010003.getCode())) {
                    LogUtil.info("案件{}存在重复发票风险。",reportNo);
                    RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                    ruleSubResultDTO.setRuleCode(RuleTypeEnums.RULE_TYPE_899999.getCode());
                    ruleSubResultDTO.setRuleReason("案件理赔规则触发规则：本案账单存在重复，或者中信险存在风险！");
                    ruleSubResultDTO.setBillNo("");
                    claimRuleResultDTO.setAutoPass(false);
                    claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
                    break;
                }
            }
        }

        if(CollectionUtils.isNotEmpty(claimRuleResultDTO.getRuleSubResultList())){
            LogUtil.info("案件：{}触发规则前置判断直接返回！：{}",claimRuleResultDTO.getRuleSubResultList());
            // 保存数据
            autoRuleRecordService.saveRuleRecord(claimRuleResultDTO);
            return claimRuleResultDTO;
        }

        CaseInfoBO caseInfoBO = getBaseCaseInfoBO(reportNo,caseTimes);
        // 理赔费用信息  正常费用发票
        List<FeeInfoDTO> feeInfoDTOS = feePayService.getFeeByClaimType(reportNo, caseTimes, "1", null);
        caseInfoBO.setIsExistingClaimFeeFlag(CollectionUtils.isNotEmpty(feeInfoDTOS));
        // 理赔二次核保记录
        Integer surrenderCount = clmsPolicySurrenderInfoService.getSurrenderCount(reportNo, caseTimes);
        caseInfoBO.setIsExistingUnderwrite(surrenderCount>0);


        BillInfoListBO billInfoListBO = new BillInfoListBO();
        // List<String> billNoList = new ArrayList<>(); 删除
        List<BillInfoBO> billDateList = new ArrayList<>();
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        for (MedicalBillInfoVO medicalBillInfoVO : Optional.ofNullable(medicalBillInfoVOS).orElseGet(Collections::emptyList)) {
            BillInfoBO billInfoBO = new BillInfoBO();
            // 处理当天投保当天报案，发票日为当天问题
            billInfoBO.setBillDate(DateUtils.endOfDay(medicalBillInfoVO.getStartDate()));

            billDateList.add(billInfoBO);
        }
        // billInfoBO.setBillNoList(billNoList); 删除
        billInfoListBO.setBillDateList(billDateList);
        caseInfoBO.setIsExistingDuplicateBillFlag(MapUtils.isNotEmpty(medicalBillService.checkAllBillDuplicate(reportNo, caseTimes)));

        // 理算赔付信息
        DutyPayInfoListBO dutyPayInfoListBO = new DutyPayInfoListBO();
        List<DutyPayInfoBO> dutyPayInfoBOS = new ArrayList<>();
        List<PolicyPayDTO>  policyPayDTOS = policyPayService.getByReportNo(reportNo, caseTimes);
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);

        try {
            for (PolicyPayDTO policyPayDTO : policyPayDTOS) {
                Optional<PolicyInfoDTO> policyInfoOpts = policyInfoDTOList.stream().filter(item -> item.getPolicyNo().equals(policyPayDTO.getPolicyNo())).findFirst();
                Optional<OcasPolicyPlanDutyDTO> ocasPolicyInfoOpts = Optional.ofNullable(ocasMapper.getPolicyInfoByPolicyNo(policyPayDTO.getPolicyNo()));
                Date insuranceBeginTime = policyPayDTO.getInsuranceBeginTime();
                Date insuranceEndTime = policyPayDTO.getInsuranceEndTime();
                if(ocasPolicyInfoOpts.isPresent()){
                    insuranceBeginTime = ocasPolicyInfoOpts.get().getInsuranceBeginDate();
                    insuranceEndTime = ocasPolicyInfoOpts.get().getInsuranceEndDate();
                }

                caseInfoBO.setPolicyNo(policyPayDTO.getPolicyNo());
                caseInfoBO.setProductCode(policyPayDTO.getProductCode());
                caseInfoBO.setInsuranceBeginDate(insuranceBeginTime);
                caseInfoBO.setInsuranceEndDate(insuranceEndTime);
                // 案件历史信息：该保单对应的已结案次数
                caseInfoBO.setEndCaseTimes(getEndCaseTimes(reportNo,policyPayDTO.getPolicyNo()));
                policyInfoOpts.ifPresent(policyInfoDTO -> caseInfoBO.setPackageCode(policyInfoDTO.getProductPackageType()));
                // 赔款+费用金额
                caseInfoBO.setPayAmount(policyPayDTO.getPolicySumPay());
                // 险种赔付信息
                for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                    caseInfoBO.setPlanCode(planPayDTO.getPlanCode());
                    // 责任赔付信息
                    Boolean isShareAmount = planPayDTO.getDutyPayArr().stream().anyMatch(DutyPayDTO::getIsShareAmount);
                    for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                        DutyPayInfoBO dutyPayInfoBO = new DutyPayInfoBO();
                        dutyPayInfoBO.setPolicyNo(policyPayDTO.getPolicyNo());
                        dutyPayInfoBO.setProductCode(policyPayDTO.getProductCode());
                        dutyPayInfoBO.setPlanCode(planPayDTO.getPlanCode());
                        dutyPayInfoBO.setInsuranceBeginDate(policyPayDTO.getInsuranceBeginTime());
                        dutyPayInfoBO.setInsuranceEndDate(policyPayDTO.getInsuranceEndTime());
                        dutyPayInfoBO.setDutyCode(dutyPayDTO.getDutyCode());
                        dutyPayInfoBO.setDutyPayAmount(dutyPayDTO.getSettleAmount());
                        if (dutyPayDTO.getSettleAmount() == null || dutyPayDTO.getSettleAmount().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }

                        // 赋值责任属性，只有类型是12的才赋值 ATTRIBUTE_CODE：'12'表示 属性配置了限额类型，6-表示配置了赔偿限额
                        Optional<DutyAttributeDTO> dutyAttr12Opts = Optional.ofNullable(dutyPayDTO.getAttributes()).orElseGet(ArrayList::new).stream()
                                .filter(item -> "12".equals(item.getAttrCode())).findFirst();
                        if(dutyAttr12Opts.isPresent()){
                            Optional<DutyAttributeDTO> dutyAttr6Opts =
                                    Optional.ofNullable(dutyPayDTO.getAttributes()).orElseGet(ArrayList::new).stream()
                                            .filter(item -> "6".equals(item.getAttrCode())).findFirst();
                            dutyPayInfoBO.setAttributeCode(dutyAttr12Opts.get().getAttrCode());
                            dutyPayInfoBO.setAttributeValue(dutyAttr12Opts.get().getAttrValue());
                            BigDecimal dailyLimit = dutyAttr6Opts.map(dutyAttributeDTO -> new BigDecimal(dutyAttributeDTO.getAttrValue())).orElse(BigDecimal.ZERO);
                            // 日限额合计
                            dutyPayInfoBO.setDutyDailyLimit(dailyLimit.multiply(BigDecimal.valueOf(billDateList.size())));
                            // 责任历史累计日限额
                            dutyPayInfoBO.setDutyHistoryDailyLimit(getDutyHistoryDailyLimit(dutyPayInfoBO,billDateList));

                        }
                        // 责任保额\ 历史累计责任给付额\ 免赔额
                        dutyPayInfoBO.setDutyTotalAmount(dutyPayDTO.getBaseAmountPay());
                        BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(null, reportNo,
                                policyPayDTO.getPolicyNo(), planPayDTO.getPlanCode(),dutyPayDTO.getDutyCode(),
                                dutyPayDTO.getIsDutyShareAmount(), dutyPayDTO.getShareDutyGroup(), isShareAmount);
                        dutyPayInfoBO.setHistoryTotalDutyPayAmount(dutyHistoryPay);
                        dutyPayInfoBO.setDutyRemitAmount(Optional.ofNullable(dutyPayDTO.getRemitAmount()).orElse(BigDecimal.ZERO));
                        dutyPayInfoBO.setCalRemainAmount(BigDecimal.ZERO);
                        dutyPayInfoBO.setCalDayPayLimit(BigDecimal.ZERO);

                        dutyPayInfoBOS.add(dutyPayInfoBO);
                    }
                }
                dutyPayInfoListBO.setDutyPayInfoList(dutyPayInfoBOS);
//                if(CollectionUtils.isEmpty(dutyPayInfoBOS)){
//                    LogUtil.info("赔付责任为空，直接返回不调自赔！");
//                    RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
//                    ruleSubResultDTO.setRuleCode(RuleTypeEnums.RULE_TYPE_899999.getCode());
//                    ruleSubResultDTO.setRuleReason("案件赔付责任为空！");
//                    ruleSubResultDTO.setBillNo("");
//                    claimRuleResultDTO.setAutoPass(false);
//                    claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
//                } else {
                    RuleResultDTO ruleResultDTO = ruleService.autoVerifyRule(caseInfoBO, billInfoListBO, dutyPayInfoListBO);
                    claimRuleResultDTO.updateRuleResult(ruleResultDTO);
//                }
            }
        } catch (Exception e) {
            LogUtil.error("executeVerifySettleRule自核规则结果异常结束:",e);
            RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
            ruleSubResultDTO.setRuleCode("999999");
            ruleSubResultDTO.setRuleReason("案件调用核赔规则异常！");
            ruleSubResultDTO.setBillNo("");
            claimRuleResultDTO.setAutoPass(false);
            claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);

        }
        // 扩展核赔规则 extendVerifyRule
        extendVerifyRule(claimRuleResultDTO);

        LogUtil.info("executeVerifySettleRule调用结束，返回结果: {}", claimRuleResultDTO);
        // 保存数据记录
        autoRuleRecordService.saveRuleRecord(claimRuleResultDTO);
        return claimRuleResultDTO;
    }

    @Override
    public ClaimRuleResultDTO IndependentVerifyRule(String reportNo, Integer caseTimes, Date insuranceBeginTime, Date insuranceEndTime) {
        ClaimRuleResultDTO  claimRuleResultDTO = new ClaimRuleResultDTO();
        claimRuleResultDTO.setReportNo(reportNo);
        claimRuleResultDTO.setCaseTimes(caseTimes);
        claimRuleResultDTO.setAutoPass(true);
        claimRuleResultDTO.setRuleType(BpmConstants.OC_SETTLE_REVIEW);
        claimRuleResultDTO.setAutoZeroCancel(false);
        claimRuleResultDTO.setRuleSubResultList(Lists.newArrayList());

        if(!switchRule){
            LogUtil.info("executeVerifySettleRule自核规则开关未开启！");
            claimRuleResultDTO.setAutoPass(false);
            return claimRuleResultDTO;
        }

        if(!Integer.valueOf(1).equals(caseTimes)){
            LogUtil.info("案件：{}重开不走自动核赔规则！",reportNo);
            claimRuleResultDTO.setAutoPass(false);
            return claimRuleResultDTO;
        }

//        CaseInfoBO caseInfoBO = getBaseCaseInfoBO(reportNo,caseTimes);
//        // 理赔费用信息  正常费用发票
//        List<FeeInfoDTO> feeInfoDTOS = feePayService.getFeeByClaimType(reportNo, caseTimes, "1", null);
//        caseInfoBO.setIsExistingClaimFeeFlag(CollectionUtils.isNotEmpty(feeInfoDTOS));
//        // 理赔二次核保记录
//        Integer surrenderCount = clmsPolicySurrenderInfoService.getSurrenderCount(reportNo, caseTimes);
//        caseInfoBO.setIsExistingUnderwrite(surrenderCount>0);


        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(medicalBillInfoVOS)) {
            RuleSubResultDTO ruleSubResultRepeat = new RuleSubResultDTO();
            ruleSubResultRepeat.setRuleReason("");
            for (MedicalBillInfoVO medicalBillInfoVO : medicalBillInfoVOS) {
                List<String> sameBillOnlyReportNo = medicalBillInfoMapper.getSameBillOnlyReportNo(medicalBillInfoVO);
                if (CollectionUtils.isNotEmpty(sameBillOnlyReportNo)) {
                    claimRuleResultDTO.setAutoPass(false);
                    ruleSubResultRepeat.setRuleCode("9000003");
                    String ruleReason = ruleSubResultRepeat.getRuleReason();
                    if (StringUtils.isNotBlank(ruleReason)) {
                        ruleReason = ruleReason + "\n";
                    }
                    ruleSubResultRepeat.setRuleReason(ruleReason + "发票号" + medicalBillInfoVO.getBillNo() + "在案件" + String.join("、", sameBillOnlyReportNo) + "中已存在;");
                }
            }
            if (StringUtils.isNotBlank(ruleSubResultRepeat.getRuleCode())) {
                claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultRepeat);
            }

        }

//        caseInfoBO.setIsExistingDuplicateBillFlag(MapUtils.isNotEmpty(medicalBillService.checkAllBillDuplicate(reportNo, caseTimes)));

        // 理算赔付信息
        List<PolicyPayDTO>  policyPayDTOS = policyPayService.getByReportNo(reportNo, caseTimes);
        List<FeeInfoDTO> feeInfoDTOS = feePayService.getFeeByClaimType(reportNo, caseTimes, "1", null);
        BigDecimal payAmount = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(policyPayDTOS)){
            PolicyPayDTO policyPayDTO = policyPayDTOS.get(0);
            BigDecimal settleAmount = policyPayDTO.getSettleAmount();
            payAmount = payAmount.add(settleAmount);

            RuleSubResultDTO ruleSubResultExceed = new RuleSubResultDTO();
            for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                    for (DutyDetailPayDTO dutyDetailPayDTO : dutyPayDTO.getDutyDetailPayArr()) {
                        if(CollectionUtils.isNotEmpty(dutyDetailPayDTO.getDetailBillSettleList())) {
                            for (ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO : dutyDetailPayDTO.getDetailBillSettleList()) {
                                if ((null != clmsDutyDetailBillSettleDTO.getSettleAmount() && clmsDutyDetailBillSettleDTO.getSettleAmount().compareTo(BigDecimal.ZERO) > 0)
                                        || (null == clmsDutyDetailBillSettleDTO.getSettleAmount() && null != clmsDutyDetailBillSettleDTO.getAutoSettleAmount() && clmsDutyDetailBillSettleDTO.getAutoSettleAmount().compareTo(BigDecimal.ZERO) > 0)) {
                                    if (DateUtils.beginOfDay(insuranceBeginTime).after(clmsDutyDetailBillSettleDTO.getBillDate())
                                            || DateUtils.endOfDay(insuranceEndTime).before(clmsDutyDetailBillSettleDTO.getBillDate())) {
                                        claimRuleResultDTO.setAutoPass(false);
                                        ruleSubResultExceed.setRuleCode("9000004");
                                        if (StringUtils.isBlank(ruleSubResultExceed.getRuleReason())) {
                                            ruleSubResultExceed.setRuleReason("发票号" + clmsDutyDetailBillSettleDTO.getBillNo() + "不在保险期间");
                                        } else {
                                            ruleSubResultExceed.setRuleReason("发票号" + clmsDutyDetailBillSettleDTO.getBillNo() + "、" + ruleSubResultExceed.getRuleReason());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(ruleSubResultExceed.getRuleCode())) {
                claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultExceed);
            }
        }

        if(CollectionUtils.isNotEmpty(feeInfoDTOS)){
            for (FeeInfoDTO feeInfoDTO : feeInfoDTOS) {
                payAmount = payAmount.add(null == feeInfoDTO.getFeeAmount() ? BigDecimal.ZERO : feeInfoDTO.getFeeAmount());
            }
        }

        if(payAmount.compareTo(new BigDecimal(3000)) >= 0){
            claimRuleResultDTO.setAutoPass(false);
            RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
            ruleSubResultDTO.setRuleCode("9000001");
            ruleSubResultDTO.setRuleReason("案件赔付金额大于等于3000元");
            claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
        }

        List<AhcsPolicyInfoEntity> policyActualPremiumList = ocasMapper.getPolicyActualPremiumList(reportNo);
        if(CollectionUtils.isNotEmpty(policyActualPremiumList)){
            if(policyActualPremiumList.get(0).getTotalActualPremium().compareTo(BigDecimal.ZERO) <= 0){
                claimRuleResultDTO.setAutoPass(false);
                RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
                ruleSubResultDTO.setRuleCode("9000002");
                ruleSubResultDTO.setRuleReason("保费未实收");
                claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
            }
        }

        List<PersonDiagnoseDTO> surgicalByTaskCode = personDiagnoseMapper.getPersonDiagnoseListByReportNo(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
        if(CollectionUtils.isNotEmpty(surgicalByTaskCode)){
            RuleSubResultDTO ruleSubResultDTO = new RuleSubResultDTO();
            StringBuilder ruleReason = new StringBuilder("就诊疾病：");
            for (PersonDiagnoseDTO personDiagnoseDTO : surgicalByTaskCode) {
                if(StringUtils.isNotBlank(personDiagnoseDTO.getDiagnoseCode()) && personDiagnoseDTO.getDiagnoseCode().contains("Q")){
                    claimRuleResultDTO.setAutoPass(false);
                    ruleSubResultDTO.setRuleCode("9000005");
                    ruleReason.append(personDiagnoseDTO.getDiagnoseName()).append(";");
                }
            }
            if(StringUtils.isNotBlank(ruleSubResultDTO.getRuleCode())){
                ruleSubResultDTO.setRuleReason(ruleReason.toString());
                claimRuleResultDTO.getRuleSubResultList().add(ruleSubResultDTO);
            }
        }
        // 保存数据记录
        autoRuleRecordService.saveRuleRecord(claimRuleResultDTO);
        return claimRuleResultDTO;
    }

    /**
     * 扩展核赔规则执行
     * @param claimRuleResultDTO
     */
    private void extendVerifyRule(ClaimRuleResultDTO  claimRuleResultDTO){
        if(!claimRuleResultDTO.isAutoPass()){
            // 如果已经不是自动理算了，则不执行逻辑
            return;
        }
        String reportNo = claimRuleResultDTO.getReportNo();
        Integer caseTimes = claimRuleResultDTO.getCaseTimes();

        /*
         * 补充规则1：检查案件被保险人公司内理赔风险：1、其它案件理赔时调查异常；2、其它案件理赔时存在二核拒保。
         */
        addCustomerClaimRisk(reportNo,claimRuleResultDTO);

        /*
         * 补充规则2：其他案件未完成调查任务或者二核任务
         */
        if(claimRuleResultDTO.isAutoPass()){
            addOtherCaseUnfinishedTask(reportNo,caseTimes,claimRuleResultDTO);
        }
    }

    /**
     * 基础案件信息查询
     * @param reportNo
     * @param caseTimes
     */
    private CaseInfoBO getBaseCaseInfoBO(String reportNo,Integer caseTimes){
        // 前置判断
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
        if(null == wholeCaseBaseDTO){
            LogUtil.info("自核调用结束,案件不存在：{}",reportNo);
            throw new GlobalBusinessException(MessageFormat.format("案件{0}类别不存在！",reportNo));
        }


        CaseInfoBO caseInfoBO = new CaseInfoBO();
        caseInfoBO.setReportNo(reportNo);
        caseInfoBO.setCaseTimes(caseTimes);
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        caseInfoBO.setAccidentCauseLevel1(reportAccident.getAccidentCauseLevel1());
        caseInfoBO.setAccidentCauseLevel2(reportAccident.getAccidentCauseLevel2());
        caseInfoBO.setAccidentDetail(reportAccident.getAccidentDetail());
        caseInfoBO.setAccidentProvinceCode(reportAccident.getProvinceCode());
        caseInfoBO.setAccidentCityCode(reportAccident.getAccidentCityCode());
        caseInfoBO.setAccidentCountyCode(reportAccident.getAccidentCountyCode());
        caseInfoBO.setPaymentType(getPaymentType(wholeCaseBaseDTO));
        List<ReportInfoExEntity> reportInfoExEntities = reportInfoExService.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfoExEntity = reportInfoExEntities.get(0);
        // 案件处理类型 TPA全包案件标记 0 非TPA案件 1 全流程 2 半流程
        caseInfoBO.setClaimDealWay(reportInfoExEntity.getClaimDealWay());
        caseInfoBO.setCompanyId(StringUtils.trimToEmpty(reportInfoExEntity.getCompanyId()));
        //案件类别判断:案件类别仅是“意健险”，且二级分类仅含“意外医疗”、“疾病住院医疗”、“疾病门急诊医疗”时，定损信息不得为空
        List<CaseClassDTO> newCaseClassList = caseClassMapper.getNewCaseClassList(reportNo, caseTimes, null);
        String parentCaseClass =
                newCaseClassList.stream().map(CaseClassDTO::getParentCode).distinct().collect(Collectors.joining(Constants.SEPARATOR));
        caseInfoBO.setCaseFirstType(parentCaseClass);
        List<CaseTypeBO> caseTypeBOList = newCaseClassList.stream().map(item -> {
            CaseTypeBO caseTypeBO = new CaseTypeBO();
            caseTypeBO.setCaseSecondType(item.getCaseSubClass());
            return caseTypeBO;
        }).collect(Collectors.toList());
        caseInfoBO.setCaseSecondTypeList(caseTypeBOList);
        // 是否存在理算退回收单。
        List<MistakeRecordDTO> mistakeRecordDTOList = mistakeRecordMapper.getSimpleMistakeRecord(reportNo, caseTimes, BpmConstants.OC_CHECK_DUTY);
        caseInfoBO.setIsReturnCheckDuty(!mistakeRecordDTOList.isEmpty() ? 1 : 0);
        // 添加案件TPA风险提示
        setCaseRiskWarningInfo(caseInfoBO);

        return caseInfoBO;
    }


    @NotNull
    private String getPaymentType(WholeCaseBaseDTO wholeCaseBaseDTO) {
        String paymentType = "";
        if(wholeCaseBaseDTO != null){
            //赔付方式(1-赔付  4-拒赔 )
            String conclusion = wholeCaseBaseDTO.getIndemnityConclusion();
            //赔偿模式(5-协议赔付,6-通融赔付)
            String indemnityModel = wholeCaseBaseDTO.getIndemnityModel();
            if ("1".equals(conclusion)) {
                paymentType = conclusion + StringUtils.trimToEmpty(indemnityModel);
            } else if ("4".equals(conclusion)) {
                paymentType = conclusion;
            }
        }
        return paymentType;
    }

    @NotNull
    private Integer getEndCaseTimes(String reportNo,String policyNo) {
        // 保单维度
        WholeCaseVO wholeCaseVO = new WholeCaseVO();
        wholeCaseVO.setPolicyNo(policyNo);

        String productPackage= ahcsPolicyPlanMapper.getPolicyProductPackage(policyNo);

        if(configMonthProjectCode.contains(productPackage)) {
            wholeCaseVO.setReportNo(reportNo);
            List<String> reportNoList =  clmsEveryMonthPayTimesCheckService.getMonthEndCase(wholeCaseVO);
            reportNoList.remove(reportNo);
            LogUtil.info("报案号：{}，保单号{}方案代码为{}，配置方案代码为：{}，故获取保单月赔付次数为：{}",reportNo,policyNo,productPackage,configMonthProjectCode,reportNoList.size());
            return reportNoList.size();

        } else {
            // 处理重开重复记问题,只根据报案号记数
            Set<String> reportNos = new HashSet<>();
            List<HistoryCaseDTO> historyCaseDTOS = reportInfoService.getHistoryCaseNew(wholeCaseVO);
            for (HistoryCaseDTO historyCaseDTO : historyCaseDTOS) {
                if(StringUtils.equals(reportNo,historyCaseDTO.getReportNo())){
                    continue;
                }

                WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(historyCaseDTO.getReportNo(),
                        historyCaseDTO.getCaseTimes());
                if (wholeCaseBaseDTO != null && ConstValues.CASE_STATUS_END.equals(wholeCaseBaseDTO.getWholeCaseStatus())) {
                    BigDecimal sumPayFee = policyPayService.getSumPayFee(historyCaseDTO.getReportNo(),
                            Integer.valueOf(historyCaseDTO.getCaseTimes()));
                    if (sumPayFee.compareTo(BigDecimal.ZERO) > 0) {
                        reportNos.add(historyCaseDTO.getReportNo());
                    }
                }
            }
            return reportNos.size();
        }

    }

    private void setCaseRiskWarningInfo(CaseInfoBO caseInfoBO) {
        String reportNo = caseInfoBO.getReportNo();
        // 添加案件TPA风险提示
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        caseInfoBO.setReportSubMode(reportInfo.getReportSubMode());
        if (StringUtils.equals(reportInfo.getRiskFlag(), ConstValues.YES)) {
            List<RiskWarningInfoBO> riskWarningInfoBOList = Arrays.stream(StringUtils.split(Optional.ofNullable(reportInfo.getRiskRemark()).orElse(""),
                            Constants.SEPARATOR)).filter(item -> null != CaseRiskTypeEnums.getName(item)).distinct()
                    .map(item -> {
                        RiskWarningInfoBO riskWarningInfoBO = new RiskWarningInfoBO();
                        riskWarningInfoBO.setCode(item);
                        riskWarningInfoBO.setDesc(CaseRiskTypeEnums.getName(item));
                        return riskWarningInfoBO;
                    }).collect(Collectors.toList());
            caseInfoBO.setRiskWarningInfoList(riskWarningInfoBOList);
        }
    }

    /**
     *  获取账单日期的历史限额
     * @param dutyPayInfoBO
     * @param billDateList
     * @return
     */
    private  BigDecimal getDutyHistoryDailyLimit(DutyPayInfoBO dutyPayInfoBO,List<BillInfoBO> billDateList) {
        List<String> billDateStrList = billDateList.stream().map(item -> DateUtils.dateFormat(item.getBillDate(),
                DateUtils.SIMPLE_DATE_STR)).collect(Collectors.toList());
        DutyBillLimitInfoDTO paramDto=new DutyBillLimitInfoDTO();
        paramDto.setPolicyNo(dutyPayInfoBO.getPolicyNo());
        paramDto.setPlanCode(dutyPayInfoBO.getPlanCode());
        paramDto.setDutyCode(dutyPayInfoBO.getDutyCode());
        paramDto.setBillDateList(billDateStrList);
        //当前报案号下所有发票日已赔付的金额总和
        List<DutyBillLimitInfoDTO>  dutyBillLimitInfoDTOS = dutyBillLimitInfoMapper.getAllPayDutyLimitDate(paramDto);
        LogUtil.info("查询责任已赔付的金额入参={}，返回查询结果={}", JsonUtils.toJsonString(paramDto),JsonUtils.toJsonString(dutyBillLimitInfoDTOS));
        if(CollectionUtils.isEmpty(dutyBillLimitInfoDTOS)){
            return BigDecimal.ZERO;
        }
        BigDecimal sumAmount=BigDecimal.ZERO;
        for (DutyBillLimitInfoDTO dto :dutyBillLimitInfoDTOS) {
            sumAmount=sumAmount.add(dto.getSettleClaimAmount());
        }
        return sumAmount;
    }

    private void getAge(ReportCustomerInfoEntity reportCustomerInfoEntity,Date insuranceBeginDate){
        if(null == reportCustomerInfoEntity
                ||  "200".equals(reportCustomerInfoEntity.getClientCluster())
                || "020".equals(reportCustomerInfoEntity.getClientCluster())){
            return;
        }
        Date birthday = reportCustomerInfoEntity.getBirthday();
        if(null == birthday || null == insuranceBeginDate){
            return;
        }
        int age = 0;
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(insuranceBeginDate);
            int currentYear = calendar.get(Calendar.YEAR);
            int currentMonth = calendar.get(Calendar.MONTH) + 1;
            int currentDayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);

            calendar.setTime(birthday);
            int yearBorn = calendar.get(Calendar.YEAR);
            int monthBorn = calendar.get(Calendar.MONTH) + 1;
            int dayOfMonthBorn = calendar.get(Calendar.DAY_OF_MONTH);

            age = currentYear - yearBorn;
            if (currentMonth < monthBorn || (currentMonth == monthBorn && currentDayOfMonth < dayOfMonthBorn)) {
                age--;
            }
            reportCustomerInfoEntity.setAge(age);
        }catch (Exception e){
           LogUtil.error("计算年龄异常",e);
        }
    }

}
