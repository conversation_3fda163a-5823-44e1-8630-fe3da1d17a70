package com.paic.ncbs.claim.service.common.impl;

import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.model.vo.taskdeal.UserWithTaskCountVO;
import com.paic.ncbs.claim.service.common.ClaimQueryOperateUserService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class ClaimQueryOperateUserServiceImpl implements ClaimQueryOperateUserService {
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private TaskPoolService taskPoolService;

    /**
     * 根据报案号，保单号查询保单承保机构下的任何一个用户
     * @param rerportNo
     * @param policyNo
     * @return
     */
    @Override
    public UserWithTaskCountVO getDepartMentCodeAnyOne(String taskDefinitionBpmKey,String rerportNo,String policyNo,String departMentCode) {
        List<UserWithTaskCountVO> userInfoList = null;
        try {
            userInfoList = taskPoolService.getTaskDealUser(taskDefinitionBpmKey,departMentCode,null);
        } catch (NcbsException e) {
            throw new RuntimeException("查询用户信息异常"+e);
        }
        if(CollectionUtils.isEmpty(userInfoList)){
            return new UserWithTaskCountVO();
        }
        return userInfoList.get(0);
    }
}
