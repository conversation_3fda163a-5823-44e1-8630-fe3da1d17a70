package com.paic.ncbs.claim.model.vo.sop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SOP信息VO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP信息")
public class SopMainVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private String idSopMain;

    @ApiModelProperty("sop名称")
    private String sopName;

    @ApiModelProperty("版本号")
    private String versionNo;

    @ApiModelProperty("批次号")
    private String batchNo;
    @ApiModelProperty("是否有暂存数据 Y/N")
    private String hasTempData;

    @ApiModelProperty("sop简要描述")
    private String sopDescription;

    @ApiModelProperty("是否全流程（Y/N）")
    private String isAllProcess;

    @ApiModelProperty("是否全文替换（Y/N）")
    private String isFullReplace;

    @ApiModelProperty("sop规则内容")
    private String sopContent;

    @ApiModelProperty("发布人员")
    private String publisherCode;

    @ApiModelProperty("发布人员姓名")
    private String publisherName;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @ApiModelProperty("是否有效（Y/N）")
    private String validFlag;

    @ApiModelProperty("生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;

    @ApiModelProperty("失效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invalidDate;

    @ApiModelProperty("状态（01-暂存、02-有效、03-无效）")
    private String status;

    @ApiModelProperty("文件类型（01-文本 02-文件 03-所有）")
    private String fileType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("SOP文件列表")
    private List<SopFileVO> fileList;

    @ApiModelProperty("适用产品列表")
    private List<String> productCodes;

    @ApiModelProperty("适用方案列表")
    private List<String> groupCodes;

    @ApiModelProperty("适用险种列表")
    private List<String> planCodes;

    @ApiModelProperty("适用产品名称列表")
    private List<String> productNames;

    @ApiModelProperty("适用方案名称列表")
    private List<String> planNames;

    @ApiModelProperty("适用险种名称列表")
    private List<String> dutyNames;

    @ApiModelProperty("产品代码（逗号分隔）")
    private String productCode;

    @ApiModelProperty("产品名称（逗号分隔）")
    private String productName;

    @ApiModelProperty("方案代码（逗号分隔）")
    private String groupCode;

    @ApiModelProperty("方案名称（逗号分隔）")
    private String groupName;

    @ApiModelProperty("险种代码（逗号分隔）")
    private String planCode;

    @ApiModelProperty("险种中文名称（逗号分隔）")
    private String planChineseName;

    @ApiModelProperty("环节代码（逗号分隔）")
    private String taskBpmKey;

    @ApiModelProperty("环节代码名称")
    private String taskBpmKeyName;


    @ApiModelProperty("历史版本信息")
    List<SopHistoryMainVO> sopHistoryMainVOS;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("文件类型名称")
    private String fileTypeName;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sysCtime;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sysUtime;


}
