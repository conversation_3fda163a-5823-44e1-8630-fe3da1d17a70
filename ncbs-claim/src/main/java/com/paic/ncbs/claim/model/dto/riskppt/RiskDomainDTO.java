package com.paic.ncbs.claim.model.dto.riskppt;

import java.util.List;

public class RiskDomainDTO {
    private String reportNo;
    private Integer caseTimes;
    private String taskId;
    private String userId;
    private List<PolicyGroupDTO> policyGroupList;
    private List<CaseRiskPropertyDTO> riskPropertyList;

    public RiskDomainDTO() {
    }

    public RiskDomainDTO(String reportNo, Integer caseTimes, String taskId, String userId, List<PolicyGroupDTO> policyGroupList, List<CaseRiskPropertyDTO> riskPropertyList) {
        this.reportNo = reportNo;
        this.caseTimes = caseTimes;
        this.taskId = taskId;
        this.userId = userId;
        this.policyGroupList = policyGroupList;
        this.riskPropertyList = riskPropertyList;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<PolicyGroupDTO> getPolicyGroupList() {
        return policyGroupList;
    }

    public void setPolicyGroupList(List<PolicyGroupDTO> policyGroupList) {
        this.policyGroupList = policyGroupList;
    }

    public List<CaseRiskPropertyDTO> getRiskPropertyList() {
        return riskPropertyList;
    }

    public void setRiskPropertyList(List<CaseRiskPropertyDTO> riskPropertyList) {
        this.riskPropertyList = riskPropertyList;
    }
}
