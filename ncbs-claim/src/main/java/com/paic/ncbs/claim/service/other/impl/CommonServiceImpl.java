package com.paic.ncbs.claim.service.other.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.RapeDateUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.mapper.other.CommonMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.CommonDTO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.document.model.dto.RequestNoDTO;
import com.paic.ncbs.document.model.dto.ResponseNoDTO;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.document.service.GenerateVoucherUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.paic.ncbs.claim.common.util.LogUtil.log;

@Service
@RefreshScope
public class CommonServiceImpl extends BaseServiceImpl<CommonDTO> implements CommonService {

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${env}")
    private String env;

    @Value("${switch.redisSeq}")
    private Boolean redisSeq;

    @Value("${initRedisSeq}")
    private Long initRedisSeq;

    private int seq_report = 1;
    private int seq_case = 1;

    @Override
    public String generateIdBySysGuid() {
        return commonMapper.generateIdBySysGuid();
    }

    @Override
    public BaseDao<CommonDTO> getDao() {
        return commonMapper;
    }

    @Override
    public String generateReportNo(String departmentCode) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "8" + departmentCode + getReportNo();
    }

    @Override
    public String generateReportNoNew(String departmentCode, String flag) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "9" + departmentCode + flag + getReportNo();
    }

    /*
     * 生成报案申请号
     */
    @Override
    public String generateApplyNo(String departmentCode, String flag) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "8" + departmentCode + flag + getApplyNo();
    }

    @Override
    public String generateCaseNo(String departmentCode) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        return "4" + departmentCode + getCaseNo();
    }

    @Override
    public String generateCaseNoNew(String departmentCode, String flag) {
        if (departmentCode == null) {
            departmentCode = "111";
        }
        if (departmentCode.length() > 3) {
            departmentCode = departmentCode.substring(0, 3);
        }
        if (departmentCode.length() < 3) {
            int diff = 3 - departmentCode.length();
            for (int i = 0; i < diff; i++) {
                departmentCode = "0" + departmentCode;
            }
        }
        String yearMonth = DateUtils.getCurrentDateFormat("YYMM");
        return "4" + departmentCode + yearMonth + flag+ getCaseNoEleven();
    }

    private String getReportNo() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_REPORT_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getReportSeq();
                log.error("redis生成报案号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getReportSeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 15 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }

    private String getApplyNo() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_APPLY_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getApplySeq();
                log.error("redis生成报案申请号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getApplySeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 15 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }

    private String getCaseNo() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_CASE_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getCaseSeq();
                log.error("redis生成赔案号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getCaseSeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 16 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }

    private String getCaseNoEleven() {
        String seq = "";
        if(redisSeq){
            try{
                RAtomicLong reportAtomicLong = redissonClient.getAtomicLong("SEQ_CLM_CASE_NO_NEW");
                if(reportAtomicLong.get() < initRedisSeq){
                    reportAtomicLong.set(initRedisSeq);
                }
                seq = String.valueOf(reportAtomicLong.incrementAndGet());
            } catch (Exception e) {
                seq = commonMapper.getCaseSeq();
                log.error("redis生成赔案号异常！，message:{}", e.getMessage(), e);
            }
        } else {
            seq = commonMapper.getCaseSeq();
        }
        StringBuilder sb = new StringBuilder();
        int diff = 11 - seq.length();
        for (int i = 0; i < diff; i++) {
            sb.append("0");
        }
        return sb.toString() + seq;
    }


    @Override
    public String generateNo(String noType, VoucherTypeEnum voucherTypeEnum, String departmentCode) {
        String generateNo = "";
        if ("zking".equals(env)) {
            RequestNoDTO requestNoDTO = new RequestNoDTO();
            requestNoDTO.setComCode(departmentCode);
            requestNoDTO.setVoucherType(voucherTypeEnum);
            requestNoDTO.setRiskCode(BaseConstant.STRING_0000);
            ResponseNoDTO generate = GenerateVoucherUtils.generate(requestNoDTO);
            if ("9999".equals(generate.getRtnCode())) {
                generate = GenerateVoucherUtils.generate(requestNoDTO);
                if ("9999".equals(generate.getRtnCode())) {
                    generate = GenerateVoucherUtils.generate(requestNoDTO);
                    if ("9999".equals(generate.getRtnCode())) {
                        throw new GlobalBusinessException("取号器异常!!!");
                    }
                }
            }
            generateNo = generate.getBillNo();
        } else {
            switch (noType) {
                case NoConstants.BEATCH_REPORT_NO:
                    String timestamp = RapeDateUtil.parseToFormatString(new Date(), "yyyyMMddHHmmssSSS"); // 17位
                    String randomPart = UuidUtil.getUUID().substring(0, 6);// 6位
                    generateNo = timestamp + randomPart; // 23位
                    // generateNo = RapeDateUtil.parseToFormatString(new Date(), "yyyyMMddHHmmssSSS");
                    break;
                case NoConstants.REPORT_NO: //报案号
                    generateNo = generateReportNoNew(departmentCode, Constants.OFFLINE_FLAG);
                    break;
                case NoConstants.CASE_NO: //赔案号
                    generateNo = generateCaseNoNew(departmentCode, Constants.OFFLINE_FLAG);
                    break;
                case NoConstants.REGIST_NO:  //立案号
                    generateNo = "5" + UuidUtil.getUUID().substring(0, 19);
                    break;
                case NoConstants.APPLY_NO://报案申请号
                    generateNo = generateApplyNo(departmentCode, Constants.OFFLINE_FLAG);
                    break;
                default:
                    generateNo = UuidUtil.getUUID();
                    break;
            }
        }
        return generateNo;
    }

    @Override
    public String generateNoOnline(String noType, VoucherTypeEnum voucherTypeEnum, String departmentCode) {
        String generateNo;
        switch (noType) {
            case NoConstants.BEATCH_REPORT_NO:
                generateNo = RapeDateUtil.parseToFormatString(new Date(), "yyyyMMddHHmmssSSS");
                break;
            case NoConstants.REPORT_NO: //报案号
                generateNo = generateReportNoNew(departmentCode,Constants.ONLINE_FLAG);
                break;
            case NoConstants.CASE_NO: //赔案号
                generateNo = generateCaseNoNew(departmentCode,Constants.ONLINE_FLAG);
                break;
            case NoConstants.REGIST_NO:  //立案号
                generateNo = "5" + UuidUtil.getUUID().substring(0, 19);
                break;
            default:
                generateNo = UuidUtil.getUUID();
                break;
        }
        return generateNo;
    }

}
