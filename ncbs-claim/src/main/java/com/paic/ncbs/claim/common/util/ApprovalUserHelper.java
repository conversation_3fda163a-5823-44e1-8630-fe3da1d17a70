package com.paic.ncbs.claim.common.util;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审批用户获取工具类
 * 提取 InvestigateTaskServiceImpl 和 EntrustmentServiceImpl 中 getApprovalUsers 方法的重复代码
 */
@Component
public class ApprovalUserHelper {

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;

    @Autowired
    private TaskPoolService taskPoolService;

    /**
     * 获取审批用户列表的通用方法
     * 
     * @param departmentCode 部门代码
     * @return 审批用户列表
     * @throws GlobalBusinessException 业务异常
     */
    public List<UserInfoDTO> getApprovalUsers(String departmentCode) throws GlobalBusinessException {
        try {
            // 1. 获取调查部门代码
            String investigateDepartment = getInvestigateDepartment();
            
            // 2. 获取子部门代码列表
            List<String> childCodeList = getChildDepartmentCodes(investigateDepartment);
            
            // 3. 获取所有部门的用户信息
            List<UserInfoDTO> userInfoDTO = getAllDepartmentUsers(childCodeList);
            
            // 4. 构建部门代码筛选列表
            List<String> departmentCodes = buildDepartmentCodes(departmentCode);
            
            // 5. 筛选用户
            return filterUsersByDepartment(userInfoDTO, departmentCodes);
            
        } catch (Exception e) {
            throw new GlobalBusinessException("获取审批用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取调查部门代码
     * 
     * @return 调查部门代码
     * @throws GlobalBusinessException 业务异常
     */
    private String getInvestigateDepartment() throws GlobalBusinessException {
        InvestigateTaskDTO investigateTask = new InvestigateTaskDTO();
        investigateTask.setInvestigateDepartment(Constants.DEPARTMENT_CODE);
        
        // 获取服务器信息列表
        TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
        List<String> serverCodeList = new ArrayList<>();
        
        if (CollectionUtils.isNotEmpty(serverInfoList)) {
            serverCodeList = serverInfoList.stream()
                    .map(ServerInfoVO::getServerCode)
                    .collect(Collectors.toList());
        }
        
        // 检查部门代码是否需要替换
        if (StringUtils.isEmptyStr(investigateTask.getInvestigateDepartment()) 
                || serverCodeList.contains(investigateTask.getInvestigateDepartment())) {
            investigateTask.setInvestigateDepartment(WebServletContext.getDepartmentCode());
        }
        
        return investigateTask.getInvestigateDepartment();
    }

    /**
     * 获取子部门代码列表
     * 
     * @param investigateDepartment 调查部门代码
     * @return 子部门代码列表
     */
    private List<String> getChildDepartmentCodes(String investigateDepartment) {
        List<String> childCodeList = new ArrayList<>();
        childCodeList.add(investigateDepartment);
        
        List<String> parentCodeList = new ArrayList<>();
        parentCodeList.add("775");
        childCodeList.addAll(departmentDefineMapper.getChildCodeList(parentCodeList));
        
        return childCodeList;
    }

    /**
     * 获取所有部门的用户信息
     * 
     * @param childCodeList 部门代码列表
     * @return 用户信息列表
     */
    private List<UserInfoDTO> getAllDepartmentUsers(List<String> childCodeList) throws NcbsException {
        List<UserInfoDTO> userInfoDTO = new ArrayList<>();
        
        for (String investigateDepartment : childCodeList) {
            String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(investigateDepartment);
            List<UserInfoDTO> departmentUserInfoDTO = taskPoolService.searchTaskDealUser(
                    investigateDepartment, BpmConstants.OC_MAJOR_INVESTIGATE);
            
            // 设置用户的部门信息
            departmentUserInfoDTO.forEach(user -> {
                user.setComCode(investigateDepartment);
                user.setComName(departmentName);
            });
            
            userInfoDTO.addAll(departmentUserInfoDTO);
        }
        
        return userInfoDTO;
    }

    /**
     * 构建部门代码筛选列表
     * 
     * @param departmentCode 部门代码
     * @return 部门代码列表
     */
    private List<String> buildDepartmentCodes(String departmentCode) {
        List<String> departmentCodes = new ArrayList<>();
        departmentCodes.add(departmentCode);
        departmentCodes.add(Constants.DEPARTMENT_CODE);
        return departmentCodes;
    }

    /**
     * 根据部门代码筛选用户
     * 
     * @param userInfoDTO 用户信息列表
     * @param departmentCodes 部门代码列表
     * @return 筛选后的用户列表
     */
    private List<UserInfoDTO> filterUsersByDepartment(List<UserInfoDTO> userInfoDTO, List<String> departmentCodes) {
        return userInfoDTO.stream()
                .filter(user -> departmentCodes.contains(user.getComCode()))
                .collect(Collectors.toList());
    }

    /**
     * 从用户列表中排除指定的用户
     * 
     * @param userInfoDTO 用户信息列表
     * @param excludeUserCodes 要排除的用户代码列表
     * @return 筛选后的用户列表
     */
    public List<UserInfoDTO> excludeUsers(List<UserInfoDTO> userInfoDTO, List<String> excludeUserCodes) {
        if (CollectionUtils.isEmpty(excludeUserCodes)) {
            return userInfoDTO;
        }
        
        return userInfoDTO.stream()
                .filter(user -> !excludeUserCodes.contains(user.getUserCode()))
                .collect(Collectors.toList());
    }
}
