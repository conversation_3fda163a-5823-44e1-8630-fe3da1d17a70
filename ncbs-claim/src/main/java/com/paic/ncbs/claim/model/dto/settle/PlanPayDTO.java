package com.paic.ncbs.claim.model.dto.settle;


import com.paic.ncbs.claim.common.constant.ModelConsts;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel("险种赔付信息")
@Data
public class PlanPayDTO extends EntityDTO {

    private static final long serialVersionUID = 1262397583511962490L;
    @ApiModelProperty("主键")
    private String idAhcsPlanPay;
    @ApiModelProperty("关联险种主键")
    private String idAhcsPolicyPlan;
    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("赔案号")
    private String caseNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("赔付批次号")
    private String idAhcsBatch;
    @ApiModelProperty("险种代码")
    private String planCode;
    @ApiModelProperty("险种名称")
    private String planName;
    @ApiModelProperty("手动理算金额")
    private BigDecimal settleAmount;
    @ApiModelProperty("险种责任赔付信息")
    private List<DutyPayDTO> dutyPayArr;
    @ApiModelProperty("仲裁费")
    private BigDecimal arbitrageFee;
    @ApiModelProperty("诉讼费")
    private BigDecimal lawsuitFee;
    @ApiModelProperty("公估费")
    private BigDecimal commonEstimateFee;
    @ApiModelProperty("律师费")
    private BigDecimal lawyerFee;
    @ApiModelProperty("执行费")
    private BigDecimal executeFee;
    @ApiModelProperty("检验费")
    private BigDecimal verifyFee;
    @ApiModelProperty("调查费")
    private BigDecimal inquireFee;
    @ApiModelProperty("其他")
    private BigDecimal otherFee;
    @ApiModelProperty("专项查勘费")
    private BigDecimal specialSurveyFee;
    @ApiModelProperty("费用总和")
    private BigDecimal feeSum;
    @ApiModelProperty("赔付类型(理算支付类型)（1-赔付 2-预陪 3-垫付 4-追偿 5-垫付转回 6-代位追偿 7-预付 8-营改增）")
    private String claimType = ModelConsts.CLAIM_TYPE_PAY;
    @ApiModelProperty("共享保额代码")
    private String groupCode;
    @ApiModelProperty("是否合并责任")
    private String isMergePolicyDuty;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveTime;
    private Integer subTimes ;
    private String idPlyRiskGroup;
    private String idPlyRiskProperty;
    @ApiModelProperty("险种赔付金额变化量")
    private BigDecimal chgPlanPayAmount;

    public void setSettleAmount(BigDecimal settleAmount){
        if (settleAmount==null){
            this.settleAmount = null;
        }else{
            this.settleAmount = settleAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }

    public List<DutyPayDTO> getDutyPayArr() {
        return dutyPayArr == null ? new ArrayList<>() : dutyPayArr;
    }
}
