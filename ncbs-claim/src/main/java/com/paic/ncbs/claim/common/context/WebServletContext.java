package com.paic.ncbs.claim.common.context;

import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

public class WebServletContext {

	private WebServletContext(){}

	public static UserInfoDTO getUser(){
		Object userObj = getRequest().getSession().getAttribute(Constants.CURR_USER);
		if( userObj == null){
			throw new GlobalBusinessException(ErrorCode.LOGIN_ERROR,"请重新登录");
		}
		return (UserInfoDTO) userObj;
	}

	public static String getUserIdForLog(){
		UserInfoDTO userInfoDTO;
		Object userObj;
		if( null != getRequest()){
			HttpSession session = getRequest().getSession();
			if(session != null){
				userObj = session.getAttribute(Constants.CURR_USER);
				if( userObj == null){
					return "system";
				}
				userInfoDTO = (UserInfoDTO) userObj;
				return userInfoDTO.getUserCode();
			}
		}

		return "system";
	}

	public static String getUserId() {
		return  getUser().getUserCode();
	}

	public static String getUserName(){
		return getUser().getUserName();
	}

	/**
	 * 进入系统勾选的权限机构
	 * @return departmentCode
	 */
	public static String getDepartmentCode(){
		if( null != getRequest()){
			HttpSession session = getRequest().getSession();
			if(session != null){
				String currComCode = (String) session.getAttribute(Constants.CURR_COMCODE);
				if(StringUtils.isNotEmpty(currComCode)){
					return currComCode;

				}
			}
		}
		return ConfigConstValues.HQ_DEPARTMENT;
	}

	/**
	 * 用户的实际归属机构，组织架构的机构
	 * @return comCode
	 */
	public static String getComCode(){ return getUser().getComCode();}


	public static HttpServletRequest getRequest() {
		RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
		return requestAttributes == null ? null : ((ServletRequestAttributes)requestAttributes).getRequest();
	}

	public static HttpServletResponse getResponse() {
		RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
		return requestAttributes == null ? null : ((ServletRequestAttributes)requestAttributes).getResponse();
	}
}
