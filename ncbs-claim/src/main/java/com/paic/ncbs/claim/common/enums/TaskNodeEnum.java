package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;
import com.paic.ncbs.claim.common.constant.BpmConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务节点枚举
 */
public enum TaskNodeEnum {
    OC_REPORT(BpmConstants.OC_REPORT, "报案", "0"),
    OC_REPORT_TRACK(BpmConstants.OC_REPORT_TRACK, "报案跟踪", "1"),
    OC_CHECK_DUTY(BpmConstants.OC_CHECK_DUTY, "收单", "1"),
    OC_MANUAL_SETTLE(BpmConstants.OC_MANUAL_SETTLE, "理算", "1"),
    OC_SETTLE_REVIEW(BpmConstants.OC_SETTLE_REVIEW, "核赔", "2"),
    OC_ZERO_CANCEL_DEPT_AUDIT(BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT, "零注审批", "2"),
    OC_COMMUNICATE(BpmConstants.OC_COMMUNICATE, "沟通", "1"),
    OC_MAJOR_INVESTIGATE(BpmConstants.OC_MAJOR_INVESTIGATE, "调查", "1"),
    OC_INVESTIGATE_APPROVAL(BpmConstants.OC_INVESTIGATE_APPROVAL, "提调审批", "2"),
    OC_INVESTIGATE_REVIEW(BpmConstants.OC_INVESTIGATE_REVIEW, "调查审批", "2"),
    OC_REGISTER_REVIEW(BpmConstants.OC_REGISTER_REVIEW, "立案审批", "2"),
    OC_PREPAY_REVIEW(BpmConstants.OC_PREPAY_REVIEW, "预赔审批", "2"),
    OC_PAY_BACK_MODIFY(BpmConstants.OC_PAY_BACK_MODIFY, "支付修改", "1"),
    OC_PAY_BACK_MODIFY_REVIEW(BpmConstants.OC_PAY_BACK_MODIFY_REVIEW, "支付修改审批", "2"),
    OC_REJECT_REVIEW(BpmConstants.OC_REJECT_REVIEW, "拒赔审批", "2"),
    OC_WAIT_CUSTOMER_SUPPLEMENTS(BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS, "客户补材", "1"),
    OC_CLAIM_SECOND_UNDERWRITING(BpmConstants.OC_CLAIM_SECOND_UNDERWRITING, "理赔二核", "0"),
    OC_RESTART_CASE_MODIFY(BpmConstants.OC_RESTART_CASE_MODIFY, "重开修改", "1"),
    OC_RESTART_CASE_APPROVAL(BpmConstants.OC_RESTART_CASE_APPROVAL, "重开审批", "2"),
    OC_FEE_INVOICE_MODIFY(BpmConstants.OC_FEE_INVOICE_MODIFY, "费用发票修改", "1"),
    OC_ESTIMATE_CHANGE_REVIEW(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW, "未决修正审批", "2"),
    OC_HUMAN_INJURY_TRACKING(BpmConstants.OC_HUMAN_INJURY_TRACKING, "人伤跟踪", "1"),
    OC_REPLEVY(BpmConstants.OC_REPLEVY,"追偿", "1"),
    OC_REPLEVY_REVIEW(BpmConstants.OC_REPLEVY_REVIEW,"追偿审批","2"),
    OC_REPLEVY_FEE_REVIEW(BpmConstants.OC_REPLEVY_FEE_REVIEW,"追偿费用审批","2"),
    OC_BLACK_LIST(BpmConstants.OC_BLACK_LIST, "黑名单审批", "2"),
    OC_NO_POLICY_REPORT(BpmConstants.OC_NO_POLICY_REPORT, "无保单报案", "1"),
    OC_ENTRUSTMENT_APPROVAL(BpmConstants.OC_ENTRUSTMENT_APPROVAL, "委托审批", "2"),
    OC_LITIGATION_APPROVAL(BpmConstants.OC_LITIGATION_APPROVAL, "诉讼批复", "2"),
    OC_LITIGATION_WAS_CONCLUDED(BpmConstants.OC_LITIGATION_WAS_CONCLUDED, "诉讼", "1"),
    OC_NEGATIVE_RESTART_CANCEL(BpmConstants.OC_NEGATIVE_RESTART_CANCEL, "负数重开取消", "1")
    ;
    private String code;
    private String name;
    private String type;

    TaskNodeEnum(String code, String name, String type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static String getName(String code) {
        if (StrUtil.isEmpty(code)) {
            return null;
        }
        for (TaskNodeEnum riskType : TaskNodeEnum.values()) {
            if (code.equals(riskType.getCode())) {
                return riskType.getName();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static List<TaskNodeEnum> getListByType(String type){
        List<TaskNodeEnum> list = new ArrayList<>();
        if(StrUtil.isEmpty(type)){
            return list;
        }
        for (TaskNodeEnum riskType : TaskNodeEnum.values()) {
            if (type.equals(riskType.getType())) {
                list.add(riskType);
            }
        }
        return list;
    }

}
