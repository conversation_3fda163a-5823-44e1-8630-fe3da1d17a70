package com.paic.ncbs.claim.model.dto.settle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.constant.ModelConsts;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@ApiModel("保单赔付信息")
@Data
public class PolicyPayDTO extends EntityDTO {

    private static final long serialVersionUID = 4953587936193997371L;
    @ApiModelProperty("主键")
    private String idClmPolicyPay;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔案号")
    private String caseNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("产品代码")
    private String productCode;
    @ApiModelProperty("产品名称")
    private String productName;
    @ApiModelProperty("方案代码")
    private String productPackage;
    @ApiModelProperty("险种赔付信息")
    private List<PlanPayDTO> planPayArr;
    @ApiModelProperty("保单总预赔")
    private BigDecimal policyPrePay;
    @ApiModelProperty("保单总预陪费用")
    private BigDecimal policyPreFee;
    @ApiModelProperty("保单总预估金额")
    private BigDecimal policySumEstimate;
    @ApiModelProperty("保单总赔付金额(赔款金额+费用(不含减损费用))")
    private BigDecimal policySumPay;
    @ApiModelProperty("赔付金额(不包含费用及注销、拒赔)")
    private BigDecimal policyPay;
    @ApiModelProperty("拒赔/注销金额")
    private BigDecimal refuseAmount;
    @ApiModelProperty("保单总费用金额 (不含减损费用)")
    private BigDecimal policySumFee;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("ahcs_policy_pay主键")
    //上面是report，下面从ahcs里补充
    private String idAhcsPolicyPay;
    @ApiModelProperty("ahcs_policy_info主键")
    private String idAhcsPolicyInfo;
    @ApiModelProperty("承保机构名称")
    private String departmentChineseName;
    @ApiModelProperty("承保机构编码")
    private String departmentCode;
    @ApiModelProperty("手动理算金额")
    private BigDecimal settleAmount;
    @ApiModelProperty("减损费用/奖励费（意健险理赔）")
    private BigDecimal policyDecreaseFee;
    @ApiModelProperty("保单赔案关系")
    private PolicyClaimCaseDTO policyClaimCase;
    @ApiModelProperty("赔付类型(1-赔付 2-预陪 3-垫付)")
    private String claimType = ModelConsts.CLAIM_TYPE_PAY;
    @ApiModelProperty("赔付模式")
    private String indemnityMode = ModelConsts.INDEMNITY_MODE_PAY;
    @ApiModelProperty("赔付结论")
    private String indemnityConclusion;
    @ApiModelProperty("保单状态")
    private String policyStatus;
    @ApiModelProperty("保单开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceBeginTime;
    @ApiModelProperty("保单结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceEndTime;
    @ApiModelProperty("共保描述")
    private String coinsuranceDesc;
    @ApiModelProperty("标的共享保额标识（Y/N）")
    private String shareInsuredAmount;
    @ApiModelProperty("该产品是否有最大给付额（Y/N）")
    private String isProductCodeNoMaxPay;
    @ApiModelProperty("产品大类")
    private String productClass;
    @ApiModelProperty("产品特约信息列表")
    private List<SpecialPromiseDTO> specialPromises;
    @ApiModelProperty("方案特约信息列表")
    private List<SpecialPromiseDTO> riskGroupSpecialPromises;
    @ApiModelProperty("方案名称")
    private String riskGroupName;
    @ApiModelProperty(value = "标的组列表")
    private List<RiskGroupDTO> riskGroupList;
    private boolean rollback;

    private  List<DutyAttributeDTO> dutyAttributeDTOList;

    @ApiModelProperty("共保标志 0 非共保1 共保")
    private String coinsuranceMark;
    @ApiModelProperty("共保信息")
    private List<CoinsureDTO> coinsuranceList;

    @ApiModelProperty("是否全额给付 0否1是")
    private String isFullPay;
    @ApiModelProperty("险种赔付金额变化量")
    private BigDecimal chgPlanPayAmount;
    /**
     * 合同月
     */
    List<PolicyMonthDto> monthDtoList;

    /**
     * 查询参数场景，
     * ex 历史案件查询时，传入history
     */
    private String scene;

    public void setSettleAmount(BigDecimal settleAmount){
        if (settleAmount==null){
            this.settleAmount = null;
        }else{
            this.settleAmount = settleAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }

    public List<PlanPayDTO> getPlanPayArr() {
        return planPayArr == null ? new ArrayList<>() : planPayArr;
    }
}
