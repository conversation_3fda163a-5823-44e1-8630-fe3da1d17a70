package com.paic.ncbs.claim.common.util;

import com.paic.ncbs.claim.exception.GlobalBusinessException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RapeStringUtils {

	public RapeStringUtils() {}
	
	public static void checkIsEmpty(Object obj, String desc) throws GlobalBusinessException {
		if (isEmptyStr(obj)) {
			throw new GlobalBusinessException(desc);
		}
	}
	
	public static boolean isEmptyStr(Object obj) {
		if (null == obj || "".equals(obj.toString()) || "".equals(obj.toString().trim())) {
			return true;
		} else {
			return false;
		}
	}
	
	public static boolean isNotEmpty(String str) {
		return !isEmptyStr(str);
	}
	
	public static boolean isEmptyStr(String str) {
		if (null == str || "".equals(str.trim())) {
			return true;
		} else {
			return false;
		}
	}
	
	public static String cancelNull(String str) {
		return str == null ? "" : str;
	}

	public static String subString(String s, int n, String endWith) {
		if (s == null) {
			return "";
		}
		Pattern p = Pattern.compile("^[\\u4e00-\\u9fa5]|[\\uff08]|[\\uff09]$");
		int i = 0, j = 0;
		char[] ch = s.toCharArray();
		char c;
		for (int k = 0; k < ch.length; k++) {
			c = ch[k];
			Matcher m = p.matcher(String.valueOf(c));
			i += m.find() ? 2 : 1;
			++j;
			if (i == n) {
				break;
			}
			if (i > n) {
				--j;
				break;
			}
		}
		return s.substring(0, j) + endWith;
	}

	public static boolean isEqualStr(String str1, String str2) {
		return cancelNull(str1).equals(cancelNull(str2));
	}

	public static String getString(Object param, String defaultStr) {
		if (isEmptyStr(param)) {
			return defaultStr;
		}
		return param.toString();
	}

	public static int getLength(String str) {
		int count = 0;
		if (str == null)
			return count;
		for (int i = 0; i < str.length(); i++) {
			if (str.charAt(i) > 128)
				count += 2;
			else
				count += 1;
		}
		return count;
	}

	public static String replace(String source, String oldStr, String newStr) {
		if (RapeStringUtils.isEmptyStr(source)) {
			return source;
		}
		return source.replace(oldStr, newStr);
	}

	public static String subString(String str, int length) {
        if (str == null) {
            return null;
        }
		StringBuffer sb = new StringBuffer("");
		int count = 0;
		for (int i = 0; i < str.length(); i++) {
			if (str.charAt(i) > 128) {
				count += 2;
			} else {
				count += 1;
			}
			if (count > length) {
				break;
			} else {
				sb.append(str.charAt(i));
			}
		}
		return sb.toString();
	}

	public static String getFileExtension(String fileName) {
		String[] arr = fileName.split("\\.");
		return arr[arr.length - 1];
	}
	
	public static String getFileType(String fileName) {
		String fileExtension = getFileExtension(fileName);
		String fileType = null;
		if ("xlsx".equals(fileExtension)) {
			fileType = "excel";
		} else if ("csv".equals(fileExtension)) {
			fileType = "csv";
		} 
		return fileType;
	}

}

