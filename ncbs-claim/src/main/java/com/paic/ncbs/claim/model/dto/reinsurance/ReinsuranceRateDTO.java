package com.paic.ncbs.claim.model.dto.reinsurance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 再保数据
 */
@Data
public class ReinsuranceRateDTO {
    private String treatyType;//再保类型 临分 合约
    //再保人
    private String reinsCode;
    /**
     * 再保公司中文名称
     */
    private String reinsName;
    /**
     * 再保公司英文名称
     */
    private String reinsEname;
    /**
     * 经办人
     */
    private String handler;
    private BigDecimal shareRate;//比例
    private String currency;//币别
    //支付项目
   // private String paymentType;
    private BigDecimal sumLoss;//总金额
    private BigDecimal paidLoss;//分摊金额
   // private List<ReinItemDTO> itemLists;

}
