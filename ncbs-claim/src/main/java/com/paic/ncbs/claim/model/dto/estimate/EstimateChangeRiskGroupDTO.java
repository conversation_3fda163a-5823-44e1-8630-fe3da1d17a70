package com.paic.ncbs.claim.model.dto.estimate;

import java.util.List;

import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EstimateChangeRiskGroupDTO {
	
    @ApiModelProperty(value = "标的组ID")
    private String idPlyRiskGroup;
    @ApiModelProperty(value = "方案编号")
    private String riskGroupNo;
    @ApiModelProperty(value = "标的组名称")
    private String riskGroupName;
    private List<CaseRiskPropertyDTO> riskPropertyInfoList;

    @ApiModelProperty("预估险种")
    private List<EstimateChangePlanDTO> estimatePlanList;


}
