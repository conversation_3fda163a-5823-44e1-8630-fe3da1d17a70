package com.paic.ncbs.claim.controller.doc;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.doc.ClaimNoticeDataNotifyParam;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.vo.doc.ClaimNoticeVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.file.model.FileResponse;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.AccidentReasonConst.HEALTH_INSURANCE;
import static com.paic.ncbs.claim.common.constant.AccidentReasonConst.INJURY_INSURANCE;
import static org.apache.commons.codec.binary.Base64.decodeBase64;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-04-26 14:12
 */
@Api(tags = "打印中心-理赔通知书(对外)")
@RestController
@RequestMapping("/public/doc/app/printAction")
@Slf4j
public class PrintNotificationController {
    @Autowired
    private PrintService printService;

    @Autowired
    private FileCommonService fileCommonService;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Value("${iobs.enable}")
    private boolean iobs;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    FileUploadController fileUploadController;
    @Autowired
    private DocumentMapper documentMapper;

    @ApiOperation("理赔-通知书查询接口(对外)")
    @GetMapping(value = "/getClaimNotice")
    public ResponseResult<Object> getClaimNotice(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") String caseTimes) {
        LogUtil.info("PrintNotificationController getClaimNotice reportNo = {} , caseTimes = {}", reportNo, caseTimes);
        Map<String, String> result = new HashMap<>();
        String url = "";
        if (StringUtils.isEmptyStr(reportNo) || StringUtils.isEmptyStr(caseTimes)) {
            return ResponseResult.fail(ErrorCode.Print.PRINT_MISS_PARAM, "参数缺失");
        }
        //获取文件field
        String fileId = printService.findFileId(reportNo, Integer.parseInt(caseTimes));
        //fileName
        if (StringUtils.isNotEmpty(fileId)) {
            result.put("pdfFlag", "1");
            try {
                UserInfoDTO userDTO = WebServletContext.getUser();
                url = fileCommonService.getDownloadUrl(fileId, null, userDTO.getUserName());
            } catch (Exception e) {
                return ResponseResult.fail(ErrorCode.Print.GET_CLAIM_NOTICE_FAIL, "fileId查找文件下载地址出错");
            }
        } else {
            result.put("pdfFlag", "2");
        }
        result.put("reportNo", reportNo);
        result.put("pdfUrl", url);
        return ResponseResult.success(result);
    }

    @PostMapping(value = "/claimNoticeDataNotify",produces = {"application/json;charset=utf-8"})
    @ApiOperation("合同文件数据异步回调")
    public Map<String, Object> postClaimNoticeDataNotify(@RequestBody ClaimNoticeDataNotifyParam param) {
        log.info("==postClaimNoticeDataNotify==start==param:{}", JSON.toJSONString(param));
        Map<String, Object> result = validateParam(param);//接口入参校验
        if (!"0".equals(result.get("code"))) {//校验不通过
            return result;
        }
        try {
            String fileBase64Str = param.getFileBase64Str();
            // 将文件上传
            String uuid = UuidUtil.getUUID();
            ByteArrayInputStream inStream = new ByteArrayInputStream(decodeBase64(fileBase64Str));
            String fileId = upload(inStream, ".pdf", uuid);
            //将文件ID存入数据库中
            if(param.getCtCode().contains("_")){
                String[] ctCode = param.getCtCode().split("_");
                String reportNo = ctCode[0];
                Integer caseTimes = Integer.valueOf(ctCode[1]);
                printService.saveClaimNoticeFileId(reportNo, caseTimes, fileId);
            } else {
                PrintEntrustDTO printEntrustDTO = new PrintEntrustDTO();
                printEntrustDTO.setIdAhcsInvestigate(param.getCtCode());
                printEntrustDTO.setFileId(fileId);
                printService.saveCommissionFileId(printEntrustDTO);
            }
            return result;
        } catch (Exception e) {
            log.error("理赔通知书异步回调error", e);
            result.put("code", "2");
            result.put("message", "回调失败");
            return result;
        }
    }

    private Map<String, Object> validateParam(ClaimNoticeDataNotifyParam param) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isEmptyStr(param.getCtCode())) {
            result.put("code", "1");
            result.put("message", "业务系统合同编号不能为空");
        } else if (StringUtils.isEmptyStr(param.getFileBase64Str())) {
            result.put("code", "1");
            result.put("message", "合同PDF文件base64数据不能为空");

        } else {
            result.put("code", "0");
            result.put("message", "成功");
            log.info("参数校验通过");
        }
        return result;
    }

    private String upload(InputStream ins, String fileType, String iobskey) {
        FileResponse fileResponse = fileCommonService.upload(ins, iobskey, iobskey + fileType);
        return fileResponse.getFileId();
    }

    @ApiOperation("理赔-通知书查询接口(对外)")
    @PostMapping(value = "/getClaimNoticeList")
    public ResponseResult<Object> getClaimNoticeList(@RequestBody StandardRequestDTO standardRequestDTO) {
        if (null != standardRequestDTO && null != standardRequestDTO.getRequestData()) {
            String jsonString = JSONObject.toJSONString(standardRequestDTO.getRequestData());
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            String reportNo = jsonObject.getString("reportNo");
            LogUtil.info("PrintNotificationController getClaimNoticeList reportNo = {}", reportNo);
            if (StringUtils.isEmptyStr(reportNo)) {
                return ResponseResult.fail(ErrorCode.Print.PRINT_MISS_PARAM, "报案号缺失");
            }
            Map<String, List<ClaimNoticeVO>> result = new HashMap<>();
            List<ClaimNoticeVO> claimNoticeVOList = new ArrayList<>();
            //获取文件field
            UserInfoDTO userDTO = WebServletContext.getUser();
            List<WholeCaseVO> fileIdList = printService.findFileIdList(reportNo);
            if (!CollectionUtils.isEmpty(fileIdList)){
                LogUtil.info("PrintNotificationController getClaimNoticeList fileIdList = {}", fileIdList);
                fileIdList.forEach(wholeCaseVO -> {
                    try {
                        boolean flag = true;
                        //查询保单号
                        List<String> policyNoByReportNoList = ahcsPolicyInfoMapper.getPolicyNoByReportNo(reportNo);
                        if (!CollectionUtils.isEmpty(policyNoByReportNoList)) {
                            for(String policyNo : policyNoByReportNoList) {
                                ProductInfoDTO prudocutInfo = ocasMapper.getPrudocutInfo(policyNo);
                                //查询产品大类
                                String productClass = prudocutInfo.getProductClass();
                                if(!(HEALTH_INSURANCE.equals(productClass) || INJURY_INSURANCE.equals(productClass))){
                                    flag = false;
                                }
                            }

                            if (flag) {
                                String url = fileCommonService.getDownloadUrl(wholeCaseVO.getFileId(), null, userDTO.getUserName());
                                if (StringUtils.isNotEmpty(url)) {
                                    ClaimNoticeVO ClaimNoticeVO = new ClaimNoticeVO();
                                    ClaimNoticeVO.setReportNo(wholeCaseVO.getReportNo());
                                    ClaimNoticeVO.setCaseTimes(wholeCaseVO.getCaseTimes());
                                    ClaimNoticeVO.setPdfUrl(url);
                                    claimNoticeVOList.add(ClaimNoticeVO);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("fileId查找文件下载地址出错", e);
                    }
                });
            }
            result.put("claimNoticeList", claimNoticeVOList);
            LogUtil.info("PrintNotificationController getClaimNoticeList claimNoticeVOList = {}", claimNoticeVOList);
            return ResponseResult.success(result);
        } else {
            return ResponseResult.fail("999999","请求参数为空");
        }
    }
    @PostMapping(value = "/claimCertificateNotify",produces = {"application/json;charset=utf-8"})
    @ApiOperation("单证签章数据异步回调")
    public Map<String, Object> claimCertificateNotify(@RequestBody ClaimNoticeDataNotifyParam param) throws IOException {
        log.info("==postclaimCertificateNotify==start==param:{}", JSON.toJSONString(param));
        Map<String, Object> result = validateParam(param);//接口入参校验
        if (!"0".equals(result.get("code"))) {//校验不通过
            return result;
        }
        FileDocumentDTO fileDocument = documentMapper.selectByPrimaryKey(param.getCtCode());
        try {
            String fileBase64Str = param.getFileBase64Str();
            String fileName = fileDocument.getDocumentName().replace("_temp", "");
            // 将文件上传
            byte[] inStream = decodeBase64(fileBase64Str);
            FileInfoDTO fileInfoDTO = new FileInfoDTO();
            fileInfoDTO.setFileContentBytes(inStream);
            //将文件ID存入数据库中
            try {
                String seq = fileUploadService.getSeq();
                fileInfoDTO.setRecordNo(seq);
                fileInfoDTO.setReportNo(fileDocument.getDocumentGroupId());
                fileInfoDTO.setFileFormat(fileDocument.getDocumentFormat());
                //神兵stg环境，文件需上传至IOBS
                String fileId = iobs ? fileUploadController.saveFilePlatform(fileInfoDTO) : fileUploadController.saveLocalTool(fileInfoDTO);
                LogUtil.audit("单证上传到文档服务器返回的 fileId=" + fileId);
                fileInfoDTO.setFileId(fileId);
                if (StringUtils.isEmptyStr(fileId)) {
                    LogUtil.audit("fileId为空，单证上传到文档服务器返回的 fileId=" + fileId);
                    result.put("code", "2");
                    result.put("message", "解析文件并保存至文档服务器异常");
                }else{
                    documentMapper.updateUpLoadPath(param.getCtCode(), fileId,fileName);
                }

            } catch (Exception e) {
                LogUtil.error("上传单证异常", e);
                result.put("code", "2");
                result.put("message", "上传单证异常");
            }
            return result;
        } catch (Exception e) {
            log.error("签章异步回调error", e);
            result.put("code", "2");
            result.put("message", "回调失败");
            return result;
        }
    }
}
