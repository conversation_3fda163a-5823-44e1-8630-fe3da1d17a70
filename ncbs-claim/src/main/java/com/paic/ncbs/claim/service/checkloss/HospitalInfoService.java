package com.paic.ncbs.claim.service.checkloss;

import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.fileupolad.HospitalImportResultVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface HospitalInfoService {

    public List<HospitalInfoVO> getValidHospitalList(String hospitalName,String reportNo);

    public String getOrgTypeByReportNo(String reportNo,String checkType);

    public HospitalInfoVO getHospitalByCode(String hospitalCode);

    public HospitalInfoVO saveHospitalInfo(HospitalInfoVO hospitalInfoVO);

    public HospitalInfoVO deleteHospitalInfo(String idHospitalInfo);

    public List<HospitalInfoVO> getHospitalInfoList(@RequestBody HospitalInfoVO hospitalInfoVO);


    HospitalImportResultVO importHospitalInfo(MultipartFile file);

    void getHospitalInfoTemplate(HttpServletResponse response);

    void getImportFailInfo(String batchNo,HttpServletResponse response) throws IOException;

    void updateHospitalCode();
}


