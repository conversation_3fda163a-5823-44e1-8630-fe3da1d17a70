package com.paic.ncbs.claim.dao.entity.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * 赔款拆分责任表(ClmsPaymentDuty)实体类
 *
 * <AUTHOR>
 * @since 2023-04-24 21:00:30
 */
@Data
public class ClmsPaymentDuty implements Serializable {
    private static final long serialVersionUID = -99925774501035743L;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;
    /**
     * 表主键
     */
    private String idClmsPaymentDuty;
    /**
     * 赔款拆分险种表CLMS_PAYMENT_PLAN主键
     */
    private String idClmsPaymentPlan;
    /**
     * 责任编码
     */
    private String dutyCode;
    /**
     * 责任分摊金额
     */
    private BigDecimal dutyPayAmount;

    /**
     * 不含税金额
     */
    private BigDecimal noTaxAmount;
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    /**
     * 责任分摊金额变化量
     */
    private BigDecimal chgDutyPayAmount;

}

