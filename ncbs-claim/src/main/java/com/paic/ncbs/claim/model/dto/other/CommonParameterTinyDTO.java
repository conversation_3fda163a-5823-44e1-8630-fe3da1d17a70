package com.paic.ncbs.claim.model.dto.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("CommonParameterTinyDTO-公共参数信息")
public class CommonParameterTinyDTO extends EntityDTO {
     
    private static final long serialVersionUID = 3411193251035068807L;

    @ApiModelProperty("机构编码")
    private String departmentCode;

    @ApiModelProperty("参数类型码")
    private String collectionCode;

    @ApiModelProperty("参数类型名称")
    private String collectionName;

    @ApiModelProperty("参数码")
    private String valueCode;

    @ApiModelProperty("参数码中文名称")
    private String valueChineseName;

    @ApiModelProperty("参数码中文简称")
    private String valueChineseAbbrName;

    @ApiModelProperty("参数类型码数组")
    private String [] collectionCodeList ;

    public String getValueChineseAbbrName() {
		return valueChineseAbbrName;
	}

	public void setValueChineseAbbrName(String valueChineseAbbrName) {
		this.valueChineseAbbrName = valueChineseAbbrName;
	}

	public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getCollectionCode() {
        return collectionCode;
    }

    public void setCollectionCode(String collectionCode) {
        this.collectionCode = collectionCode;
        this.collectionCodeList = collectionCode.split(",");
    }

    public String getCollectionName() {
        return collectionName;
    }

    public void setCollectionName(String collectionName) {
        this.collectionName = collectionName;
    }

    public String getValueCode() {
        return valueCode;
    }

    public void setValueCode(String valueCode) {
        this.valueCode = valueCode;
    }

    public String getValueChineseName() {
        return valueChineseName;
    }

    public void setValueChineseName(String valueChineseName) {
        this.valueChineseName = valueChineseName;
    }

    public String[] getCollectionCodeList() {
        return collectionCode.split(",");
    }

}
