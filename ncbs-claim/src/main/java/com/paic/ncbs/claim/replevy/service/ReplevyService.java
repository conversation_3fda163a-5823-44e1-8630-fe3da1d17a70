package com.paic.ncbs.claim.replevy.service;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.replevy.vo.ClmsRelatedActualReceiptVo;
import com.paic.ncbs.claim.replevy.vo.ReplevyApiVo;
import com.paic.ncbs.claim.replevy.vo.ReplevyReqApiVo;
import com.paic.ncbs.claim.replevy.vo.SendCashFlowSearchVo;

import java.util.List;

/**
 * <p>
 * 追偿明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface ReplevyService {
    /**
     *追偿保存提交接口
     * @return
     */
    public ResponseResult<Object> saveOrSubmitReplevy(ReplevyApiVo replevyApiVo);

    /**
     * 追偿子页面保存提交，此接口不生成任务流，只保存数据
     * @param replevyApiVo
     * @return
     */
    public ResponseResult<Object> saveReplevyDetail(ReplevyApiVo replevyApiVo);

    /**
     * 直接理赔费用提交
     * @param replevyApiVo
     * @return
     */
    public ResponseResult<Object> saveOrSubmitReplevyFee(ReplevyApiVo replevyApiVo);

    /**
     *关联实收调用收付流水查询
     * @param sendReplevyVo
     * @return
     */
    public ResponseResult<Object> sendCashFlowSearch(SendCashFlowSearchVo sendReplevyVo);

    /**
     *追偿页面初始化
     * @param replevyApiVo
     * @return
     */
    public ResponseResult<Object> initReplevy(ReplevyApiVo replevyApiVo);

    /**
     *追偿审核页面初始化
     * @param replevyApiVo
     * @return
     */
    public ResponseResult<Object> initReplevyApprove(ReplevyApiVo replevyApiVo);

    /**
     * 查看按钮页面初始化
     */
    public ResponseResult<Object> initReplevyView(ReplevyApiVo replevyApiVo);

    /**
     * 报送首付冻结，解冻
     *
     * @param freezeFlag F-冻结，R-释放
     */
    public void sendPayThaw(String reportNo,Integer subTimes,String replevyNo,String freezeFlag,String receiptType);

    /**
     * 统一删除追偿相关数据
     * 根据initFlag区分删除类型：1-删除费用信息，2-删除追偿明细信息，3-删除关联实收数据
     * @param replevyApiVo 删除请求参数
     * @return
     */
    public ResponseResult<Object> deleteReplevyData(ReplevyApiVo replevyApiVo);

    /**
     * 查询险种和责任信息
     * @return 包含险种和责任信息的PlanAndDutyQueryDTO
     */
    public ResponseResult<Object> queryPlanAndDutyInfo(ReplevyApiVo replevyApiVo);

    /**
     * 根据报案号查询追偿主表信息
     * @param replevyApiVo
     * @return 追偿主表查询结果包装对象
     */
    public ResponseResult<Object> queryReplevyMainByReportNo(ReplevyApiVo replevyApiVo);

    public ResponseResult<Object> checkReplevy(ReplevyReqApiVo replevyReqApiVo);
    public void saveOrUpdateReplevyActualReceipt(List<ClmsRelatedActualReceiptVo> relatedActualReceiptList, String reportNo, int caseTimes, String businessId, String receiptType);
}
