
package com.paic.ncbs.claim.service.other.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.GlobalNodeStatus;
import com.paic.ncbs.claim.common.enums.TaskNodeEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.estimate.EstimateChangeApplyEntity;
import com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity;
import com.paic.ncbs.claim.dao.entity.report.ActivitiTaskUrlCfg;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.MarketProductInfoEntityMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayHisInfo;
import com.paic.ncbs.claim.model.dto.duty.DutyPayHisInfo;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeRiskGroupDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayHisInfo;
import com.paic.ncbs.claim.model.dto.duty.DutyPayHisInfo;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeRiskGroupDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.ClmsTaskConflictDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListAuditVO;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkReqVo;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkResVo;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.model.vo.taskdeal.*;
import com.paic.ncbs.claim.model.vo.user.DepartmentUserVO;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.IEstimateChangeApplyService;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListAuditService;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyMainMapper;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyMainVo;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.lawsuit.ClmsLawsuitCaseService;
import com.paic.ncbs.claim.service.openapi.OpenGlobalService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.taskdeal.ITaskInfoPlusService;
import com.paic.ncbs.claim.service.taskdeal.TaskConflictService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.lambda.Seq;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.BpmConstants.MAIN_TASK_MAP;

@Slf4j
@Service
@RefreshScope
public class TaskListService {

    @Autowired
    private TaskInfoMapper taskInfoMapper ;

    @Autowired
    @Lazy
    TaskPoolService taskPoolService;

    @Autowired
    CacheService cacheService;

    @Autowired
    TaskConflictService taskConflictService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private IEstimateChangeApplyService estimateChangeApplyService;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper ;

    @Autowired
    private MarketProductInfoEntityMapper marketProductInfoEntityMapper;

    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @Autowired
    private FeePayMapper feePayMapper;

    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;

    @Autowired
    private PolicyPayMapper policyPayMapper;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private EntrustmentAuditMapper entrustmentAuditMapper;

    @Value("${es.url:http://ncbs-policy-query.lb.ssdev.com:48047/query-policy/es/queryClaimTaskInfoByParam}")
    private String esUrl;

    @Value("${es.rows:2000}")
    private Integer rows;

    @Value("${es.page:1}")
    private Integer page;

    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Value("${es.alarmCount:10}")
    private Integer alarmCount;

    @Value("${es.switch}")
    private String esNacos;

    @Value("${es.diffAutoSyncFlag: N}")
    private String diffAutoSyncFlag;

    @Autowired
    protected RestTemplate restTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ITaskInfoPlusService taskInfoPlusService;

    @Value("${global.dealUrl}")
    private String globalDealUrl;

    @Value("${global.auditUrl}")
    private String globalAuditUrl;

    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @Value("${global.aes.key}")
    private String globalAesKey;

    @Value("${global.jumpUrl}")
    private String globalJumpUrl;

    @Autowired
    private OpenGlobalService openGlobalService;
    @Autowired
    private ClmsReplevyMainMapper clmsReplevyMainMapper;
    @Autowired
    private ClmsReplevyChargeMapper clmsReplevyChargeMapper;

    @Autowired
    private DepartmentUserMapper departmentUserMapper;

    @Value("${global.countUrl}")
    private String countUrl;
    @Autowired
    private EntrustmentService entrustmentService;


    @Autowired
    private ClmsBlackListAuditService clmsBlackListAuditService;

    @Autowired
    private ClmsLawsuitCaseService clmsLawsuitCaseService;

    @Autowired
    private CaseProcessService caseProcessService;

    public Map<String, Object> getUserTaskList_oc(Map<String, Object> param) {
        String userId = (String) param.get("userId");
        String taskDefinitionBpmCode = (String) param.get("taskDefinitionBpmCode");
        RapeCheckUtil.checkParamEmpty(taskDefinitionBpmCode, "不能为空！");

        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> queryList = new ArrayList<>();
        map.put("queryList", queryList);

        try {
            List<Map<String, String>> taskList = taskInfoMapper.getTaskList(userId, taskDefinitionBpmCode);
            if (taskList == null || taskList.isEmpty()) {
                return map;
            }
            for (Map<String, String> taskMap : taskList) {
                taskMap.put("remarks", "");
                taskMap.put("openWindowName", BpmConstants.TASK_MAP.get(taskDefinitionBpmCode));
                taskMap.put("taskName", BpmConstants.TASK_MAP.get(taskDefinitionBpmCode));
                taskMap.put("waitTime", RapeDateUtil.getTimeBetweenDate(taskMap.get("claimTime"), RapeDateUtil.getCurrentTimeStr("yyyy-MM-dd HH:mm:ss")));
                taskMap.put("taskUrl", BpmConstants.TASK_URL_MAP.get(taskDefinitionBpmCode));
                if(BpmConstants.OC_COMMUNICATE.equals(taskDefinitionBpmCode)){
                    taskMap.put("idAhcsCommunicateBase",taskMap.get("taskId"));
                }
                if(BpmConstants.OC_INVESTIGATE_APPROVAL.equals(taskDefinitionBpmCode)){
                    taskMap.put("idAhcsInvestigate",taskMap.get("taskId"));
                }
                if(BpmConstants.OC_MAJOR_INVESTIGATE.equals(taskDefinitionBpmCode)){
                    taskMap.put("idAhcsInvestigateTask",taskMap.get("taskId"));
                }
                if(BpmConstants.OC_INVESTIGATE_REVIEW.equals(taskDefinitionBpmCode)){
                    taskMap.put("idAhcsInvestigateTaskAudit",taskMap.get("taskId"));
                }
                if(BpmConstants.OC_REGISTER_REVIEW.equals(taskDefinitionBpmCode)){
                    taskMap.put("idAhcsInvestigateTaskReject",taskMap.get("taskId"));
                }
            }
            ActivitiTaskUrlCfg dto = new ActivitiTaskUrlCfg();
            dto.setDataSource("ah");
            dto.setTaskDefinitionBpmCode(taskDefinitionBpmCode);
            queryList.add(putTaskListInfo(taskList, dto, taskList.size()));

        } catch (Exception e) {
            throw new GlobalBusinessException("查询个人工作台异常:" + e.getMessage(),e);
        }
        return map;
    }

    public List<Map<String, Object>> getTaskCountByUserId_oc(Map<String, Object> param) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < BpmConstants.OCHCS_BPM_ORDER.size(); i++) {
            String defKey = BpmConstants.OCHCS_BPM_ORDER.get(i);
            Map<String, Object> taskInfoMap = new HashMap<>();
            taskInfoMap.put("code", defKey);
            taskInfoMap.put("fieldSetLabel", BpmConstants.TASK_MAP.get(defKey));
            taskInfoMap.put("totalCount", taskInfoMapper.getTaskCount(defKey));
            taskInfoMap.put("isWorkFlag", "Y");
            list.add(taskInfoMap);
        }
        return list;
    }

    private Map<String, Object> putTaskListInfo(List<Map<String, String>> list,ActivitiTaskUrlCfg dto,Integer totalSize){
        Map<String, Object> taskInfoMap = new HashMap<>();
        taskInfoMap.put("code", dto.getTaskDefinitionBpmCode());
        taskInfoMap.put("list", list);
        taskInfoMap.put("dataSource", dto.getDataSource());
        taskInfoMap.put("totalCount", totalSize);
        return taskInfoMap;
    }

    public TaskCountVO getTaskCount(TaskCountVO taskCountVO) throws Exception{
        UserInfoDTO user = WebServletContext.getUser();
        LambdaQueryWrapper<TaskInfoDTO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        String departmentCode = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            departmentCode = departmentUserVO.getDepartmentCode();
        }
        // 包含下级机构
        List<String> departmentCodes = getAllDepartmentCodesByCode(departmentCode);
//        lambdaQueryWrapper.in(TaskInfoDTO::getDepartmentCode, departmentCodes);
        lambdaQueryWrapper.in(TaskInfoDTO::getStatus, Arrays.asList("0", "3"));
//        if(WorkBenchTaskQueryVO.MY_CASE_Y.equals(taskCountVO.getIsMyCase())){
//            lambdaQueryWrapper.eq(TaskInfoDTO::getAssigner, user.getUserCode());
//        }else {
//            lambdaQueryWrapper.and(wrapper -> wrapper.isNull(TaskInfoDTO::getAssigner).or().eq(TaskInfoDTO::getAssigner, ""));
//        }

//        Map<String, Long> workCount = new HashMap<>();
//        Map<String, Long> auditCount = new HashMap<>();
//        long total = 0L;
//        List<TaskNodeEnum> listByWork = TaskNodeEnum.getListByType("1");
//        for (TaskNodeEnum taskNodeEnum : listByWork) {
//            LambdaQueryWrapper<TaskInfoDTO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(TaskInfoDTO::getTaskDefinitionBpmKey, taskNodeEnum.getCode());
//            lambdaQueryWrapper.in(TaskInfoDTO::getStatus, Arrays.asList("0", "3"));
//
//            List<TaskInfoDTO> taskList = taskInfoPlusService.list(lambdaQueryWrapper);
//            if(CollectionUtils.isEmpty(taskList)){
//                continue;
//            }
//            taskList = taskList.stream().filter(i -> departmentCodes.contains(i.getDepartmentCode())).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(taskList)){
//                continue;
//            }
//
//            if(WorkBenchTaskQueryVO.MY_CASE_Y.equals(taskCountVO.getIsMyCase())){
//                taskList = taskList.stream().filter(i -> user.getUserCode().equals(i.getAssigner())).collect(Collectors.toList());
//            }else {
//                taskList = taskList.stream().filter(i -> Objects.isNull(i.getAssigner()) || "".equals(i.getAssigner())).collect(Collectors.toList());
//            }
//            if(CollectionUtils.isEmpty(taskList)){
//                continue;
//            }
//            long count = taskList.size();
//            workCount.put(taskNodeEnum.getCode(), count);
//            total = total + count;
//        }
//        taskCountVO.setWorkCount(workCount);
//
//        List<TaskNodeEnum> listByAudit = TaskNodeEnum.getListByType("2");
//        for (TaskNodeEnum taskNodeEnum : listByAudit) {
//            LambdaQueryWrapper<TaskInfoDTO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(TaskInfoDTO::getTaskDefinitionBpmKey, taskNodeEnum.getCode());
//            lambdaQueryWrapper.in(TaskInfoDTO::getStatus, Arrays.asList("0", "3"));
//
//            List<TaskInfoDTO> taskList = taskInfoPlusService.list(lambdaQueryWrapper);
//            if(CollectionUtils.isEmpty(taskList)){
//                continue;
//            }
//            taskList = taskList.stream().filter(i -> departmentCodes.contains(i.getDepartmentCode())).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(taskList)){
//                continue;
//            }
//
//            if(WorkBenchTaskQueryVO.MY_CASE_Y.equals(taskCountVO.getIsMyCase())){
//                taskList = taskList.stream().filter(i -> user.getUserCode().equals(i.getAssigner())).collect(Collectors.toList());
//            }else {
//                taskList = taskList.stream().filter(i -> Objects.isNull(i.getAssigner()) || "".equals(i.getAssigner())).collect(Collectors.toList());
//            }
//            if(CollectionUtils.isEmpty(taskList)){
//                continue;
//            }
//            long count = taskList.size();
//            auditCount.put(taskNodeEnum.getCode(), count);
//            total = total + count;
//        }
//        taskCountVO.setAuditCount(auditCount);
//        taskCountVO.setTotal(total);


        List<TaskInfoDTO> taskList = taskInfoPlusService.list(lambdaQueryWrapper);
        if(CollectionUtils.isEmpty(taskList)){
            return taskCountVO;
        }
        taskList = taskList.stream().filter(i -> departmentCodes.contains(i.getDepartmentCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(taskList)){
            return taskCountVO;
        }

        if(WorkBenchTaskQueryVO.MY_CASE_Y.equals(taskCountVO.getIsMyCase())){
            taskList = taskList.stream().filter(i -> user.getUserCode().equals(i.getAssigner())).collect(Collectors.toList());
        }else {
            taskList = taskList.stream().filter(i -> Objects.isNull(i.getAssigner()) || "".equals(i.getAssigner())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(taskList)){
            return taskCountVO;
        }
        Map<String, Long> workCount = new HashMap<>();
        Map<String, Long> auditCount = new HashMap<>();
        long total = 0L;
        List<TaskNodeEnum> listByWork = TaskNodeEnum.getListByType("1");
        for (TaskNodeEnum taskNodeEnum : listByWork) {
            long count = taskList.stream().filter(i -> taskNodeEnum.getCode().equals(i.getTaskDefinitionBpmKey())).count();
            if(count > 0){
                workCount.put(taskNodeEnum.getCode(), count);
                total = total + count;
            }
        }
        taskCountVO.setWorkCount(workCount);

        List<TaskNodeEnum> listByAudit = TaskNodeEnum.getListByType("2");
        for (TaskNodeEnum taskNodeEnum : listByAudit) {
            long count = taskList.stream().filter(i -> taskNodeEnum.getCode().equals(i.getTaskDefinitionBpmKey())).count();
            if(count > 0){
                auditCount.put(taskNodeEnum.getCode(), count);
                total = total + count;
            }
        }
        taskCountVO.setAuditCount(auditCount);
        taskCountVO.setTotal(total);
        return taskCountVO;
    }

    public Map<String, List<WorkBenchTaskVO>> getWorkBenchTaskList(WorkBenchTaskQueryVO workBenchTaskQueryVO){
        UserInfoDTO user = WebServletContext.getUser();
        workBenchTaskQueryVO.setUserCode(user.getUserCode());
        String departmentCode = workBenchTaskQueryVO.getDepartmentCode();
        String productCode = workBenchTaskQueryVO.getProductCode();
        String packageName = workBenchTaskQueryVO.getPackageName();
        String businessNo = workBenchTaskQueryVO.getBusinessNo();
        if (StringUtils.isEmptyStr(departmentCode)){
            departmentCode = WebServletContext.getDepartmentCode();
        }
        workBenchTaskQueryVO.setDepartmentCode(departmentCode);
        if (WorkBenchTaskQueryVO.INCLUDE_SUBORDINATES_Y.equals(workBenchTaskQueryVO.getIsIncludeSubordinates())){
            // 包含下级机构
            List<String> departmentCodes = getAllDepartmentCodesByCode(departmentCode);
            workBenchTaskQueryVO.setDepartmentCodes(departmentCodes);
        }
        List<String> taskDefinitionBpmKeys = new ArrayList<>();
        if (!WorkBenchTaskQueryVO.MY_CASE_Y.equals(workBenchTaskQueryVO.getIsMyCase())){
            if (StringUtils.isEmptyStr(workBenchTaskQueryVO.getTaskDefinitionBpmKey())) {
                try {
                    List<UserGradeInfoDTO> userGradeInfos = cacheService.queryUserGradeList(user.getUserCode(), WebServletContext.getDepartmentCode());
                    List<String> gradeNames = userGradeInfos.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toList());
                    NcbsConstant.GRADE_MAP.forEach((k, v)->{
                        if (CollectionUtils.containsAny(gradeNames, Arrays.asList(v.split(",")))){
                            taskDefinitionBpmKeys.add(k);
                        }
                    });
                   /* NcbsConstant.GRADE_MAP.forEach((k, v)->{
                        if (gradeNames.contains(v)) {
                            taskDefinitionBpmKeys.add(k);
                        } else if (BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS.equals(k)
                                && CollectionUtils.containsAny(gradeNames, Arrays.asList(v.split(",")))) {
                            // 客户补材支持三个岗位
                            taskDefinitionBpmKeys.add(k);
                        }
                    });*/
                    workBenchTaskQueryVO.setTaskDefinitionBpmKeys(taskDefinitionBpmKeys);
                } catch (Exception e) {
                    LogUtil.info("getWorkBenchTaskList--error");
                    workBenchTaskQueryVO.setTaskDefinitionBpmKeys(null);
                }
            } else {
                taskDefinitionBpmKeys.add(workBenchTaskQueryVO.getTaskDefinitionBpmKey());
                workBenchTaskQueryVO.setTaskDefinitionBpmKeys(taskDefinitionBpmKeys);
            }
        }
        if(StringUtils.isNotEmpty(packageName)) {
            workBenchTaskQueryVO.setPackageName(packageName.substring(packageName.lastIndexOf("_")+1,packageName.length()));
        }

        if(StringUtils.isNotEmpty(workBenchTaskQueryVO.getIsQuickPay())) {
            workBenchTaskQueryVO.setIsQuickPay("Y".equals(workBenchTaskQueryVO.getIsQuickPay())? "1": null);
        }

        //判断是否通过ES查询
        List<WorkBenchTaskVO> workBenchTaskList;
        String esSwitch = getESQuerySwitch();
        if ("Y".equals(esSwitch)) {
            DateFormat dftDay = new SimpleDateFormat("yyyy-MM-dd");
            workBenchTaskList = new ArrayList<WorkBenchTaskVO>();
            ClaimESTaskInfoQueryVO claimESTaskInfoQueryVO = new ClaimESTaskInfoQueryVO();
            List<ClaimESPolicyInfoVO> taskPolicyInfoList = new ArrayList<ClaimESPolicyInfoVO>();
            //处理机构
            List<String> departmentCodes = new ArrayList<String>();
            if (WorkBenchTaskQueryVO.INCLUDE_SUBORDINATES_Y.equals(workBenchTaskQueryVO.getIsIncludeSubordinates())){
                departmentCodes = workBenchTaskQueryVO.getDepartmentCodes();
            } else {
                if (StringUtils.isNotEmpty(workBenchTaskQueryVO.getDepartmentCode())) {
                    departmentCodes.add(workBenchTaskQueryVO.getDepartmentCode());
                }
            }
            claimESTaskInfoQueryVO.setDepartmentCodes(departmentCodes);
            //派工日期
            claimESTaskInfoQueryVO.setAssignStartTime(workBenchTaskQueryVO.getAssignStartTime());
            claimESTaskInfoQueryVO.setAssignEndTime(workBenchTaskQueryVO.getAssignEndTime());
            //报案号
            claimESTaskInfoQueryVO.setReportNo(workBenchTaskQueryVO.getReportNo());
            //赔案号
            claimESTaskInfoQueryVO.setCaseNo(workBenchTaskQueryVO.getCaseNo());
            //被保险人
            claimESTaskInfoQueryVO.setInsuredName(workBenchTaskQueryVO.getInsuredName());
            //报案号、被保险人名称、保单号
            claimESTaskInfoQueryVO.setBusinessNo(businessNo);
            //任务分类
            List<String> taskDefinitionBpmKeyList = new ArrayList<String>();
            if (WorkBenchTaskQueryVO.MY_CASE_Y.equals(workBenchTaskQueryVO.getIsMyCase())){
                claimESTaskInfoQueryVO.setIsMyCase("Y");
            } else {
                if (ListUtils.isNotEmpty(workBenchTaskQueryVO.getTaskDefinitionBpmKeys())) {
                    taskDefinitionBpmKeyList = workBenchTaskQueryVO.getTaskDefinitionBpmKeys();
                }
                claimESTaskInfoQueryVO.setIsMyCase("N");
            }
            if (StringUtils.isNotEmpty(workBenchTaskQueryVO.getTaskDefinitionBpmKey())) {
                taskDefinitionBpmKeyList.add(workBenchTaskQueryVO.getTaskDefinitionBpmKey());
            }
            claimESTaskInfoQueryVO.setTaskDefinitionBpmKeys(taskDefinitionBpmKeyList);
            //当前处理人
            claimESTaskInfoQueryVO.setAssigner(workBenchTaskQueryVO.getUserCode());
            //待处理等级
            claimESTaskInfoQueryVO.setAuditGrade(workBenchTaskQueryVO.getAuditGrade());
            //快赔
            if (StringUtils.isNotEmpty(workBenchTaskQueryVO.getIsQuickPay())) {
                claimESTaskInfoQueryVO.setIsQuickPay(workBenchTaskQueryVO.getIsQuickPay());
            }
            //状态
            List<String> statuss = new ArrayList<String>();
            statuss.add("0");
            statuss.add("3");
            claimESTaskInfoQueryVO.setStatuss(statuss);
            //保单信息
            if (StringUtils.isNotEmpty(workBenchTaskQueryVO.getPolicyNo()) || StringUtils.isNotEmpty(workBenchTaskQueryVO.getProductCode()) || StringUtils.isNotEmpty(workBenchTaskQueryVO.getPackageName())) {
                ClaimESPolicyInfoVO claimESPolicyInfoVO = new ClaimESPolicyInfoVO();
                claimESPolicyInfoVO.setPolicyNo(workBenchTaskQueryVO.getPolicyNo());
                claimESPolicyInfoVO.setProductcode(workBenchTaskQueryVO.getProductCode());
                claimESPolicyInfoVO.setSchemeName(workBenchTaskQueryVO.getPackageName());
                taskPolicyInfoList.add(claimESPolicyInfoVO);
                claimESTaskInfoQueryVO.setTaskPolicyInfoList(taskPolicyInfoList);
            }
            if (0 != workBenchTaskQueryVO.getCurrentPage()) {
                page = workBenchTaskQueryVO.getCurrentPage();
            }
            if (0 != workBenchTaskQueryVO.getPerPageSize()) {
                rows = workBenchTaskQueryVO.getPerPageSize();
            }
            claimESTaskInfoQueryVO.setPage(page);
            claimESTaskInfoQueryVO.setRows(rows);
            //调用ES接口查询数据
            List<ClaimESTaskInfoVO> claimESInfoVOList = this.claimRequestES(claimESTaskInfoQueryVO);

            //预查询所有互斥规则配置
            List<ClmsTaskConflictDTO> confilictList = taskConflictService.list();

            //预查询所有未完结任务
            List<TaskInfoDTO> taskinfoList = taskInfoMapper.getUndealCaseList();

            if (ListUtils.isNotEmpty(claimESInfoVOList)) {
                claimESInfoVOList.forEach(claimESTaskInfoVO -> {
                    WorkBenchTaskVO workBenchTaskVO = new WorkBenchTaskVO();
                    BeanUtils.copyProperties(claimESTaskInfoVO, workBenchTaskVO, WorkBenchTaskVO.class);
                    workBenchTaskVO.setPolicyDepartmentName(claimESTaskInfoVO.getDepartmentName());
                    workBenchTaskVO.setSubmitName(claimESTaskInfoVO.getApplyerName());
                    //整案时长
                    if (null != claimESTaskInfoVO.getCaseLengthDate()) {
                        Long nowTimes = System.currentTimeMillis();
                        Long caseLengthDateTimes = claimESTaskInfoVO.getCaseLengthDate().getTime();
                        Long caseLengthHour = (nowTimes-caseLengthDateTimes)/(60*60*1000);
                        workBenchTaskVO.setCaseLength(caseLengthHour.toString());
                    }
                    //当前时长
                    if (null != claimESTaskInfoVO.getCreatedDate()) {
                        try {
                            Long nowTimes = dftDay.parse(dftDay.format(new Date())).getTime();
                            Long createdDateTimes = dftDay.parse(dftDay.format(claimESTaskInfoVO.getCreatedDate())).getTime();
                            Long currentLengthDay = (nowTimes-createdDateTimes)/(24*60*60*1000);
                            workBenchTaskVO.setCurrentLength(currentLengthDay.toString());
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    String subProcessFlag = "00";
                    //判断是否主流程
                    if(MAIN_TASK_MAP.containsKey(claimESTaskInfoVO.getTaskDefinitionBpmKey()) && "0".equals(claimESTaskInfoVO.getStatus())) {
                        //判断是否有未处理任务
                        List<TaskInfoDTO> subTaskInfos = Seq.seq(taskinfoList)
                                .filter(taskInfo ->
                                        taskInfo.getReportNo().equals(claimESTaskInfoVO.getReportNo()) &&
                                                taskInfo.getCaseTimes().equals(claimESTaskInfoVO.getCaseTimes()) &&
                                                !taskInfo.getTaskDefinitionBpmKey().equals(claimESTaskInfoVO.getTaskDefinitionBpmKey())
                                )
                                .toList();

                        if (subTaskInfos != null && subTaskInfos.size() > 0) {
                            //判断ClmsTaskConflict表是否有配置的互斥规则
                            boolean flag = Seq.seq(subTaskInfos)
                                    .anyMatch(taskInfo ->
                                            Seq.seq(confilictList)
                                                    .anyMatch(confilict ->
                                                            taskInfo.getTaskDefinitionBpmKey().equals(confilict.getConflictTaskKey())
                                                                    && "1".equals(confilict.getPlanOperation())
                                                                    && claimESTaskInfoVO.getTaskDefinitionBpmKey().equals(confilict.getTaskDefinitionBpmKey())
                                                    )
                                    );
                            if (flag) {
                                subProcessFlag = "10";
                            } else {
                                subProcessFlag = "11";
                            }
                        }
                    }
                    workBenchTaskVO.setSubProcessFlag(subProcessFlag);

                    //操作 Y:处理/N：认领
                    if (StringUtils.isNotEmpty(claimESTaskInfoVO.getAssigner()) && workBenchTaskQueryVO.getUserCode().equals(claimESTaskInfoVO.getAssigner())) {
                        workBenchTaskVO.setOperate("Y");
                    } else {
                        workBenchTaskVO.setOperate("N");
                    }
                    //沟通任务跳转用到id
                    workBenchTaskVO.setIdAhcsCommunicateBase(claimESTaskInfoVO.getTaskId());
                    //任务等级
                    if (null != claimESTaskInfoVO.getTaskGrade()) {
                        workBenchTaskVO.setTaskGrade(Constants.TASK_GRADE_MAP.get(claimESTaskInfoVO.getTaskGrade()));
                    }
                    //审核等级
                    if (null != claimESTaskInfoVO.getAuditGrade()) {
                        workBenchTaskVO.setAuditGrade(Constants.AUDIT_GRADE_MAP.get(claimESTaskInfoVO.getAuditGrade()));
                    }
                    List<ClaimESPolicyInfoVO> claimESPolicyInfoVOList = claimESTaskInfoVO.getTaskPolicyInfoList();
                    if (ListUtils.isNotEmpty(claimESPolicyInfoVOList)) {
                        //保单号
                        String policyNo = "";
                        Set<String> riskGroupNameList = new HashSet<String>();
                        for (int i=0;i<=claimESPolicyInfoVOList.size()-1;i++) {
                            if (i == claimESPolicyInfoVOList.size()-1) {
                                policyNo = policyNo + claimESPolicyInfoVOList.get(i).getPolicyNo();
                            } else {
                                policyNo = policyNo + claimESPolicyInfoVOList.get(i).getPolicyNo() + ",";
                            }
                            riskGroupNameList.add(claimESPolicyInfoVOList.get(i).getSchemeName());
                        }
                        //方案名称
                        int count = 1;
                        String riskGroupName = "";
                        //产品名称
                        String productName = claimESPolicyInfoVOList.get(0).getProductName();
                        for (String riskGroupNameStr : riskGroupNameList) {
                            if (count == riskGroupNameList.size()) {
                                riskGroupName = riskGroupName + riskGroupNameStr;
                            } else {
                                riskGroupName = riskGroupName + riskGroupNameStr + ",";
                            }
                            count ++;
                        }
                        workBenchTaskVO.setPolicyNo(policyNo);
                        workBenchTaskVO.setProductName(productName);
                        workBenchTaskVO.setRiskGroupName(riskGroupName);
                    }
                    workBenchTaskList.add(workBenchTaskVO);
                });
            }
        } else {
            workBenchTaskList = taskInfoMapper.getWorkBenchTaskList(workBenchTaskQueryVO);
        }
        if(ListUtils.isEmptyList(workBenchTaskList)){
            return new HashMap<>();
        }

        // 理赔白名单？
        List<CommonParameterTinyDTO> commonParameterTinyDTOList = commonParameterMapper.getCommonParameterList(new String[]{"LIMIT_POLICY_WHITE"});

        workBenchTaskList.forEach(vo -> {
            if(StringUtils.isNotEmpty(vo.getEstimateChangeAmount())){
                //有修正金额展示修正金额
                vo.setEstimateAmount(vo.getEstimateChangeAmount());
            }
            vo.setLimitPolicyDealFlag("N");
            if(!ListUtils.isEmptyList(commonParameterTinyDTOList)){
                for (CommonParameterTinyDTO commonParameterTinyDTO : commonParameterTinyDTOList) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(commonParameterTinyDTO.getValueCode())) {
                        String [] policyNoStrArray = commonParameterTinyDTO.getValueCode().split("-");
                        if (policyNoStrArray.length > 1) {
                            String userCodeStr = policyNoStrArray[0];
                            String policyNoStr = policyNoStrArray[1];
                            if (vo.getPolicyNo().contains(policyNoStr)) {
                                vo.setLimitPolicyDealFlag("Y");
                                if (userCodeStr.equals(user.getUserCode())) {
                                    vo.setLimitPolicyDealFlag("N");
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (BpmConstants.OC_FEE_INVOICE_MODIFY.equals(vo.getTaskDefinitionBpmKey())){
                //如果包含费用发票修改，查询费用发票信息
                WorkBenchTaskVO taskVo = feePayMapper.getInvoiceInfoByReportNo(vo.getReportNo(),vo.getCaseTimes(), vo.getTaskId());
                //追偿发票查询
                if(taskVo == null){
                    taskVo = feePayMapper.getReplevyInvoiceInfoByReportNo(vo.getReportNo(),vo.getCaseTimes(),vo.getTaskId());
                }
                if (taskVo != null) {
                    vo.setCaseNo(taskVo.getCaseNo());
                    vo.setFeeAmount(taskVo.getFeeAmount());
                    vo.setReturnDate(taskVo.getReturnDate());
                    vo.setReturnType("支付退回");
                    vo.setReturnReason(taskVo.getReturnReason());
                    vo.setIsModifiedFlag(taskVo.getIsModifiedFlag());
                }
            }
            //黑名单审批任务
            if (BpmConstants.OC_BLACK_LIST.equals(vo.getTaskDefinitionBpmKey())){
                ClmsBlackListAuditVO auditVO = clmsBlackListAuditService.getPendingAuditByBlackListId(vo.getTaskId());
                if (auditVO != null) {
                    vo.setBlackListId(auditVO.getBlackListId());
                    vo.setPartyName(auditVO.getPartyName());
                    vo.setIdType(auditVO.getIdType());
                    vo.setIdTypeName(auditVO.getIdTypeName());
                    vo.setIdNum(auditVO.getIdNum());
                    vo.setPhoneNum(auditVO.getPhoneNum());
                    vo.setRiskType(auditVO.getRiskType());
                    vo.setRiskTypeName(auditVO.getRiskTypeName());
                    vo.setBlackSource(auditVO.getBlackSource());
                    vo.setRelatedReportNo(auditVO.getRelatedReportNo());
                }
            }
            // 支付修改审批任务
            if(BpmConstants.OC_PAY_BACK_MODIFY_REVIEW.equals(vo.getTaskDefinitionBpmKey())
                || BpmConstants.OC_PAY_BACK_MODIFY.equals(vo.getTaskDefinitionBpmKey())){
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setIdClmPaymentItem(vo.getTaskId().split("_")[0]);
                List<PaymentItemDTO> paymentItemList = paymentItemService.getAllPaymentItem(paymentItemDTO);
                if(CollectionUtils.isNotEmpty(paymentItemList) && paymentItemList.size() == 1){
                    PaymentItemDTO paymentItem = paymentItemList.get(0);
                    vo.setClmPaymentTypeName(paymentItem.getPaymentTypeName());// 理赔支出类型
                    vo.setPaymentAmount(BaseConstant.STRING_1.equals(paymentItem.getIsFullPay()) ? paymentItem.getCoinsuranceActualAmount() : paymentItem.getPaymentAmount()); // 共保取coinsuranceActualAmount
                }
            }

            if(BpmConstants.OC_SETTLE_REVIEW.equals(vo.getTaskDefinitionBpmKey())){
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setReportNo(vo.getReportNo());
                paymentItemDTO.setCaseTimes(vo.getCaseTimes());
                List<PaymentItemDTO> payItemList = paymentItemMapper.getPaymentItem(paymentItemDTO);
                if(!ListUtils.isEmptyList(payItemList)){
                    BigDecimal settleAmount = payItemList.stream()
                            .filter(item -> "13".equals(item.getPaymentType()))
                            .map(PaymentItemDTO::getPaymentAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal dutyFee = payItemList.stream()
                            .filter(item -> "1J".equals(item.getPaymentType()))
                            .map(PaymentItemDTO::getPaymentAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setSettleAmount(settleAmount);
                    vo.setDutyFee(dutyFee);

                }
            }
            if(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW.equals(vo.getTaskDefinitionBpmKey())){
                BigDecimal estimateAmount = BigDecimal.ZERO;
                BigDecimal sumEstimateChangeAmount = BigDecimal.ZERO;
                BigDecimal estimateChangeDifference =  BigDecimal.ZERO;
                List<EstimateChangeApplyEntity> changeApplyEntityList = estimateChangeApplyService.lambdaQuery()
                        .eq(EstimateChangeApplyEntity::getReportNo, vo.getReportNo())
                        .eq(EstimateChangeApplyEntity::getCaseTimes, vo.getCaseTimes())
                        .eq(EstimateChangeApplyEntity::getAuditStatus, BaseConstant.STRING_1).list();
                EstimateChangeApplyEntity estimateChangeApply = changeApplyEntityList.get(0);
                estimateAmount = Optional.ofNullable(vo.getEstimateAmount())
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(s -> {
                            try {
                                return new BigDecimal(s);
                            } catch (NumberFormatException e) {
                                return BigDecimal.ZERO;
                            }
                        })
                        .orElse(BigDecimal.ZERO);
                sumEstimateChangeAmount = estimateChangeApply.getApplyAmount();
                estimateChangeDifference = sumEstimateChangeAmount.subtract(estimateAmount);
                vo.setEstimateChangeDifference(estimateChangeDifference);//未决修正差额
                vo.setSumEstimateChangeAmount(sumEstimateChangeAmount);//未决修正金额合计
            }
            if(BpmConstants.OC_REPLEVY_REVIEW.equals(vo.getTaskDefinitionBpmKey())||BpmConstants.OC_REPLEVY.equals(vo.getTaskDefinitionBpmKey())){//追偿审批
                ClmsReplevyMainVo clmsReplevyMainVo = clmsReplevyMainMapper.selectNotFinished(vo.getReportNo());
                if ( clmsReplevyMainVo != null){
                    vo.setReplevyNo(clmsReplevyMainVo.getReplevyNo());
                    vo.setSerialNo(clmsReplevyMainVo.getReplevyTimes());
                    vo.setReplevyAmount(clmsReplevyMainVo.getSumRealReplevy());
                }
            }
            if(BpmConstants.OC_REPLEVY_FEE_REVIEW.equals(vo.getTaskDefinitionBpmKey())){
                ClmsReplevyCharge clmsReplevyCharge = clmsReplevyChargeMapper.selectById(vo.getBusinessKey());
                if(clmsReplevyCharge != null){
                    vo.setReplevyNo(clmsReplevyCharge.getReplevyNo());
                    vo.setSerialNo(clmsReplevyCharge.getSerialNo());
                    vo.setReplevyFee(clmsReplevyCharge.getChargeMoney());
                }
            }
            // 第三方委托审批任务
            if (BpmConstants.OC_ENTRUSTMENT_APPROVAL.equals(vo.getTaskDefinitionBpmKey())){
                EntrustAuditDTO entrustmentAuditDTO = entrustmentAuditMapper.selectById(vo.getTaskId());
                entrustmentService.convertThirdPartyTypeName(entrustmentAuditDTO);
                if(entrustmentAuditDTO != null){
                    vo.setIdAhcsCommunicateBase(entrustmentAuditDTO.getIdEntrustMain());
                    vo.setPolicyNo(entrustmentAuditDTO.getPolicyNo());
                    vo.setInsuredName(entrustmentAuditDTO.getInsuredName());
                    vo.setThirdPartyType(entrustmentAuditDTO.getThirdPartyType());
                    vo.setThirdPartyName(entrustmentAuditDTO.getThirdPartyName());
                    vo.setEntrustDpmName("01".equals(entrustmentAuditDTO.getThirdPartyType()) ?
                            entrustmentAuditDTO.getEntrustDptName():entrustmentAuditDTO.getEntrustName());
                }
            }
            if (BpmConstants.OC_LITIGATION_APPROVAL.equals(vo.getTaskDefinitionBpmKey())){
                String insuredNameByReportNo = feePayMapper.getInsuredNameByReportNo(vo.getReportNo());
                CaseProcessDTO caseProcessInfo = caseProcessService.getCaseProcessInfo(vo.getReportNo(), vo.getCaseTimes());
                ClmsLawsuitAuditCaseVO lawsuitCase = clmsLawsuitCaseService.getLawsuitCaseById(vo.getTaskId());
                if (lawsuitCase != null){
                    vo.setTaskId(lawsuitCase.getId());
                    vo.setLawsuitNo(lawsuitCase.getLawsuitNo());
                    vo.setReportNo(lawsuitCase.getReportNo());
                    vo.setPolicyNo(lawsuitCase.getPolicyNo());
                    vo.setInsuredName(insuredNameByReportNo);
                    vo.setCaseStatus(CaseProcessStatus.getName(caseProcessInfo.getProcessStatus()));
                    vo.setSubmitter(lawsuitCase.getSubmitter());
                    vo.setSubmitName(lawsuitCase.getSubmitterName());
                }
            }
            if (BpmConstants.OC_LITIGATION_WAS_CONCLUDED.equals(vo.getTaskDefinitionBpmKey())){
                String insuredNameByReportNo = feePayMapper.getInsuredNameByReportNo(vo.getReportNo());
                CaseProcessDTO caseProcessInfo = caseProcessService.getCaseProcessInfo(vo.getReportNo(), vo.getCaseTimes());
                ClmsLawsuitAuditCaseVO lawsuitCase = clmsLawsuitCaseService.getLawsuitCaseById(vo.getTaskId());
                if (lawsuitCase != null){
                    vo.setTaskId(lawsuitCase.getId());
                    vo.setLawsuitNo(lawsuitCase.getLawsuitNo());
                    vo.setReportNo(lawsuitCase.getReportNo());
                    vo.setPolicyNo(lawsuitCase.getPolicyNo());
                    vo.setInsuredName(insuredNameByReportNo);
                    vo.setCaseStatus(CaseProcessStatus.getName(caseProcessInfo.getProcessStatus()));
                    vo.setSubmitter(lawsuitCase.getSubmitter());
                    vo.setSubmitName(lawsuitCase.getSubmitterName());
                }
            }
        });
        return workBenchTaskList.stream().collect(Collectors.groupingBy(WorkBenchTaskVO::getTaskDefinitionBpmKey));
    }

    /**
     * 根据机构号查出所有下级机构
     * @param departmentCode
     * @return
     */
    public List<String> getAllDepartmentCodesByCode(String departmentCode) {
        DepartmentVO departmentVO = taskPoolService.getSelectDepartmentList4Query(departmentCode);
        List<String> departmentCodes = new ArrayList<>();
        this.getDepartmentCodes(departmentVO,departmentCodes);
        return departmentCodes;
    }

    /**
     * 递归获取子机构列表
     * @param departmentVO
     * @param departmentCodes
     */
    public void getDepartmentCodes(DepartmentVO departmentVO, List<String> departmentCodes) {
        departmentCodes.add(departmentVO.getDepartmentCode());
        if (departmentVO.getChildDepartmentVO() != null){
            for (DepartmentVO vo : departmentVO.getChildDepartmentVO()) {
                departmentCodes.add(vo.getDepartmentCode());
                getDepartmentCodes(vo,departmentCodes);
            }
        }
    }

    public List<WorkBenchTaskVO> getSelfTaskList(WorkBenchTaskQueryVO workBenchTaskQueryVO) {
        //判断是否通过ES查询
        List<WorkBenchTaskVO> workBenchTaskList;
        String esSwitch = getESQuerySwitch();
        if ("Y".equals(esSwitch)) {
            workBenchTaskList = new ArrayList<WorkBenchTaskVO>();
            ClaimESTaskInfoQueryVO claimESTaskInfoQueryVO = new ClaimESTaskInfoQueryVO();
            //处理机构
            claimESTaskInfoQueryVO.setDepartmentCodes(workBenchTaskQueryVO.getDepartmentCodes());
            //任务分类
            List<String> taskDefinitionBpmKeyList = new ArrayList<String>();
            taskDefinitionBpmKeyList.add(workBenchTaskQueryVO.getTaskDefinitionBpmKey());
            claimESTaskInfoQueryVO.setTaskDefinitionBpmKeys(taskDefinitionBpmKeyList);
            //是否本人案件
            claimESTaskInfoQueryVO.setIsMyCase(workBenchTaskQueryVO.getIsMyCase());
            claimESTaskInfoQueryVO.setAssigner(workBenchTaskQueryVO.getUserCode());
            //状态
            List<String> statuss = new ArrayList<String>();
            statuss.add("0");
            statuss.add("3");
            claimESTaskInfoQueryVO.setStatuss(statuss);
            if (0 != workBenchTaskQueryVO.getCurrentPage()) {
                page = workBenchTaskQueryVO.getCurrentPage();
            }
            if (0 != workBenchTaskQueryVO.getPerPageSize()) {
                rows = workBenchTaskQueryVO.getPerPageSize();
            }
            claimESTaskInfoQueryVO.setPage(page);
            claimESTaskInfoQueryVO.setRows(rows);
            //调用ES接口查询数据
            List<ClaimESTaskInfoVO> claimESInfoVOList = this.claimRequestES(claimESTaskInfoQueryVO);
            if (ListUtils.isNotEmpty(claimESInfoVOList)) {
                DateFormat dftDay = new SimpleDateFormat("yyyy-MM-dd");
                claimESInfoVOList.forEach(claimESTaskInfoVO -> {
                    WorkBenchTaskVO workBenchTaskVO = new WorkBenchTaskVO();
                    BeanUtils.copyProperties(claimESTaskInfoVO, workBenchTaskVO, WorkBenchTaskVO.class);
                    workBenchTaskVO.setPolicyDepartmentName(claimESTaskInfoVO.getDepartmentName());
                    workBenchTaskVO.setSubmitName(claimESTaskInfoVO.getApplyerName());
                    //整案时长
                    if (null != claimESTaskInfoVO.getCaseLengthDate()) {
                        Long nowTimes = System.currentTimeMillis();
                        Long caseLengthDateTimes = claimESTaskInfoVO.getCaseLengthDate().getTime();
                        Long caseLengthHour = (nowTimes-caseLengthDateTimes)/(60*60*1000);
                        workBenchTaskVO.setCaseLength(caseLengthHour.toString());
                    }
                    //当前时长
                    if (null != claimESTaskInfoVO.getCreatedDate()) {
                        try {
                            Long nowTimes = dftDay.parse(dftDay.format(new Date())).getTime();
                            Long createdDateTimes = dftDay.parse(dftDay.format(claimESTaskInfoVO.getCreatedDate())).getTime();
                            Long currentLengthDay = (nowTimes-createdDateTimes)/(24*60*60*1000);
                            workBenchTaskVO.setCurrentLength(currentLengthDay.toString());
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    //操作 Y:处理/N：认领
                    if (StringUtils.isNotEmpty(claimESTaskInfoVO.getAssigner()) && workBenchTaskQueryVO.getUserCode().equals(claimESTaskInfoVO.getAssigner())) {
                        workBenchTaskVO.setOperate("Y");
                    } else {
                        workBenchTaskVO.setOperate("N");
                    }
                    //沟通任务跳转用到id
                    workBenchTaskVO.setIdAhcsCommunicateBase(claimESTaskInfoVO.getTaskId());
                    //任务等级
                    if (null != claimESTaskInfoVO.getTaskGrade()) {
                        workBenchTaskVO.setTaskGrade(Constants.TASK_GRADE_MAP.get(claimESTaskInfoVO.getTaskGrade()));
                    }
                    //审核等级
                    if (null != claimESTaskInfoVO.getAuditGrade()) {
                        workBenchTaskVO.setAuditGrade(Constants.AUDIT_GRADE_MAP.get(claimESTaskInfoVO.getAuditGrade()));
                    }
                    List<ClaimESPolicyInfoVO> claimESPolicyInfoVOList = claimESTaskInfoVO.getTaskPolicyInfoList();
                    if (ListUtils.isNotEmpty(claimESPolicyInfoVOList)) {
                        //保单号
                        String policyNo = "";
                        Set<String> riskGroupNameList = new HashSet<String>();
                        for (int i=0;i<=claimESPolicyInfoVOList.size()-1;i++) {
                            if (i == claimESPolicyInfoVOList.size()-1) {
                                policyNo = policyNo + claimESPolicyInfoVOList.get(i).getPolicyNo();
                            } else {
                                policyNo = policyNo + claimESPolicyInfoVOList.get(i).getPolicyNo() + ",";
                            }
                            riskGroupNameList.add(claimESPolicyInfoVOList.get(i).getSchemeName());
                        }
                        //方案名称
                        int count = 1;
                        String riskGroupName = "";
                        //产品名称
                        String productName = claimESPolicyInfoVOList.get(0).getProductName();
                        for (String riskGroupNameStr : riskGroupNameList) {
                            if (count == riskGroupNameList.size()) {
                                riskGroupName = riskGroupName + riskGroupNameStr;
                            } else {
                                riskGroupName = riskGroupName + riskGroupNameStr + ",";
                            }
                            count ++;
                        }
                        workBenchTaskVO.setPolicyNo(policyNo);
                        workBenchTaskVO.setProductName(productName);
                        workBenchTaskVO.setRiskGroupName(riskGroupName);
                    }
                    workBenchTaskList.add(workBenchTaskVO);
                });
            }
        } else {
            workBenchTaskList = taskInfoMapper.getWorkBenchTaskList(workBenchTaskQueryVO);
        }
        List<CommonParameterTinyDTO> commonParameterTinyDTOList = commonParameterMapper.getCommonParameterList(new String[]{"LIMIT_POLICY_WHITE"});
        workBenchTaskList.forEach(vo -> {
            if(StringUtils.isNotEmpty(vo.getEstimateChangeAmount())){
                //有修正金额展示修正金额
                vo.setEstimateAmount(vo.getEstimateChangeAmount());
            }
            vo.setLimitPolicyDealFlag("N");
            if(!ListUtils.isEmptyList(commonParameterTinyDTOList)){
                for (CommonParameterTinyDTO commonParameterTinyDTO : commonParameterTinyDTOList) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(commonParameterTinyDTO.getValueCode())) {
                        String [] policyNoStrArray = commonParameterTinyDTO.getValueCode().split("-");
                        if (policyNoStrArray.length > 1) {
                            String userCodeStr = policyNoStrArray[0];
                            String policyNoStr = policyNoStrArray[1];
                            if (vo.getPolicyNo().contains(policyNoStr)) {
                                vo.setLimitPolicyDealFlag("Y");
                                if (userCodeStr.equals(workBenchTaskQueryVO.getUserCode())) {
                                    vo.setLimitPolicyDealFlag("N");
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (BpmConstants.OC_FEE_INVOICE_MODIFY.equals(vo.getTaskDefinitionBpmKey())){
                //如果包含费用发票修改，查询费用发票信息
                WorkBenchTaskVO taskVo = feePayMapper.getInvoiceInfoByReportNo(vo.getReportNo(),vo.getCaseTimes(),vo.getTaskId());
                //追偿发票查询
                if(taskVo == null){
                    taskVo = feePayMapper.getReplevyInvoiceInfoByReportNo(vo.getReportNo(),vo.getCaseTimes(),vo.getTaskId());
                }
                if (taskVo != null) {
                    vo.setCaseNo(taskVo.getCaseNo());
                    vo.setFeeAmount(taskVo.getFeeAmount());
                    vo.setReturnDate(taskVo.getReturnDate());
                    vo.setReturnType("发票退回");
                    vo.setReturnReason(taskVo.getReturnReason());
                    vo.setIsModifiedFlag(taskVo.getIsModifiedFlag());
                }
            }
            // 支付修改审批任务
            if(BpmConstants.OC_PAY_BACK_MODIFY_REVIEW.equals(vo.getTaskDefinitionBpmKey())
                || BpmConstants.OC_PAY_BACK_MODIFY.equals(vo.getTaskDefinitionBpmKey())){
                PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
                paymentItemDTO.setIdClmPaymentItem(vo.getTaskId().split("_")[0]);
                List<PaymentItemDTO> paymentItemList = paymentItemService.getAllPaymentItem(paymentItemDTO);
                if(CollectionUtils.isNotEmpty(paymentItemList) && paymentItemList.size() == 1){
                    PaymentItemDTO paymentItem = paymentItemList.get(0);
                    vo.setClmPaymentTypeName(paymentItem.getPaymentTypeName());// 理赔支出类型
                    // 支出费用金额
                    vo.setPaymentAmount(BaseConstant.STRING_1.equals(paymentItem.getIsFullPay()) ? paymentItem.getCoinsuranceActualAmount() : paymentItem.getPaymentAmount()); // 共保取coinsuranceActualAmount
                }
            }
            if(BpmConstants.OC_REPLEVY_REVIEW.equals(vo.getTaskDefinitionBpmKey())||BpmConstants.OC_REPLEVY.equals(vo.getTaskDefinitionBpmKey())){//追偿审批
                ClmsReplevyMainVo clmsReplevyMainVo = clmsReplevyMainMapper.selectNotFinished(vo.getReportNo());
                if ( clmsReplevyMainVo != null){
                    vo.setReplevyNo(clmsReplevyMainVo.getReplevyNo());
                    vo.setSerialNo(clmsReplevyMainVo.getReplevyTimes());
                    vo.setReplevyAmount(clmsReplevyMainVo.getSumRealReplevy());
                }
                if ( clmsReplevyMainVo != null){
                    vo.setReplevyNo(clmsReplevyMainVo.getReplevyNo());
                    vo.setSerialNo(clmsReplevyMainVo.getReplevyTimes());
                    vo.setReplevyAmount(clmsReplevyMainVo.getSumRealReplevy());
                }
            }
            if(BpmConstants.OC_REPLEVY_FEE_REVIEW.equals(vo.getTaskDefinitionBpmKey())){
                ClmsReplevyCharge clmsReplevyCharge = clmsReplevyChargeMapper.selectById(vo.getBusinessKey());
                if(clmsReplevyCharge != null){
                    vo.setReplevyNo(clmsReplevyCharge.getReplevyNo());
                    vo.setSerialNo(clmsReplevyCharge.getSerialNo());
                    vo.setReplevyFee(clmsReplevyCharge.getChargeMoney());
                }
            }
            //黑名单审批任务
            if (BpmConstants.OC_BLACK_LIST.equals(vo.getTaskDefinitionBpmKey())){
                ClmsBlackListAuditVO auditVO = clmsBlackListAuditService.getPendingAuditByBlackListId(vo.getTaskId());
                if (auditVO != null) {
                    vo.setBlackListId(auditVO.getBlackListId());
                    vo.setPartyName(auditVO.getPartyName());
                    vo.setIdType(auditVO.getIdType());
                    vo.setIdTypeName(auditVO.getIdTypeName());
                    vo.setIdNum(auditVO.getIdNum());
                    vo.setPhoneNum(auditVO.getPhoneNum());
                    vo.setRiskType(auditVO.getRiskType());
                    vo.setRiskTypeName(auditVO.getRiskTypeName());
                    vo.setBlackSource(auditVO.getBlackSource());
                    vo.setRelatedReportNo(auditVO.getRelatedReportNo());
                }
            }
            // 第三方委托审批任务
            if (BpmConstants.OC_ENTRUSTMENT_APPROVAL.equals(vo.getTaskDefinitionBpmKey())){
                EntrustAuditDTO entrustmentAuditDTO = entrustmentAuditMapper.selectById(vo.getTaskId());
                entrustmentService.convertThirdPartyTypeName(entrustmentAuditDTO);
                if(entrustmentAuditDTO != null){
                    vo.setIdAhcsCommunicateBase(entrustmentAuditDTO.getIdEntrustMain());
                    vo.setPolicyNo(entrustmentAuditDTO.getPolicyNo());
                    vo.setInsuredName(entrustmentAuditDTO.getInsuredName());
                    vo.setThirdPartyType(entrustmentAuditDTO.getThirdPartyType());
                    vo.setThirdPartyName(entrustmentAuditDTO.getThirdPartyName());
                    vo.setEntrustDpmName("01".equals(entrustmentAuditDTO.getThirdPartyType()) ?
                            entrustmentAuditDTO.getEntrustDptName():entrustmentAuditDTO.getEntrustName());
                }
            }
            if (BpmConstants.OC_LITIGATION_APPROVAL.equals(vo.getTaskDefinitionBpmKey())){
                String insuredNameByReportNo = feePayMapper.getInsuredNameByReportNo(vo.getReportNo());
                CaseProcessDTO caseProcessInfo = caseProcessService.getCaseProcessInfo(vo.getReportNo(), vo.getCaseTimes());
                ClmsLawsuitAuditCaseVO lawsuitCase = clmsLawsuitCaseService.getLawsuitCaseById(vo.getTaskId());
                if (lawsuitCase != null){
                    vo.setTaskId(lawsuitCase.getId());
                    vo.setLawsuitNo(lawsuitCase.getLawsuitNo());
                    vo.setReportNo(lawsuitCase.getReportNo());
                    vo.setPolicyNo(lawsuitCase.getPolicyNo());
                    vo.setInsuredName(insuredNameByReportNo);
                    vo.setCaseStatus(CaseProcessStatus.getName(caseProcessInfo.getProcessStatus()));
                    vo.setSubmitter(lawsuitCase.getSubmitter());
                    vo.setSubmitName(lawsuitCase.getSubmitterName());
                }
            }
            if (BpmConstants.OC_LITIGATION_WAS_CONCLUDED.equals(vo.getTaskDefinitionBpmKey())){
                String insuredNameByReportNo = feePayMapper.getInsuredNameByReportNo(vo.getReportNo());
                CaseProcessDTO caseProcessInfo = caseProcessService.getCaseProcessInfo(vo.getReportNo(), vo.getCaseTimes());
                ClmsLawsuitAuditCaseVO lawsuitCase = clmsLawsuitCaseService.getLawsuitCaseById(vo.getTaskId());
                if (lawsuitCase != null){
                    vo.setTaskId(lawsuitCase.getId());
                    vo.setLawsuitNo(lawsuitCase.getLawsuitNo());
                    vo.setReportNo(lawsuitCase.getReportNo());
                    vo.setPolicyNo(lawsuitCase.getPolicyNo());
                    vo.setInsuredName(insuredNameByReportNo);
                    vo.setCaseStatus(CaseProcessStatus.getName(caseProcessInfo.getProcessStatus()));
                    vo.setSubmitter(lawsuitCase.getSubmitter());
                    vo.setSubmitName(lawsuitCase.getSubmitterName());

                }
            }
        });
        return workBenchTaskList;
    }

    public List<MarketProductInfoVO> getAllMarketProductInfo(Map<String, String> map) {
        return marketProductInfoEntityMapper.getAllMarketProductInfo(map);
    }

    public List<GroupInfoVO> getAllRiskGroupInfo(Map<String, String> map) {
        return marketProductInfoEntityMapper.getAllRiskGroupInfo(map);
    }

    /**
     * ES获取理赔最新任务信息
     * @param idAhcsTaskInfo 任务主键
     * @return 任务信息
     */
    public ClaimTaskInfoToESVO getClaimESTaskInfoList (String idAhcsTaskInfo){
        ClaimTaskInfoToESVO claimTaskInfoToESVO = null;
        List<ClaimTaskInfoToESVO> taskInfoList = taskInfoMapper.getClaimESTaskInfoList(idAhcsTaskInfo);
        if (ListUtils.isNotEmpty(taskInfoList)) {
            claimTaskInfoToESVO = taskInfoList.get(0);
            if (null == claimTaskInfoToESVO.getAssigner()) {
                claimTaskInfoToESVO.setAssigner("");
            }
        }
        return claimTaskInfoToESVO;
    }

    /**
     * 理赔调用ES查询数据
     * @param claimESTaskInfoQueryVO
     * @return
     */
    public List<ClaimESTaskInfoVO> claimRequestES(ClaimESTaskInfoQueryVO claimESTaskInfoQueryVO) {
        List<ClaimESTaskInfoVO> claimESInfoVOList = new ArrayList<>();
        ClaimESTaskInfoResultVO claimESTaskInfoResultVO = this.requestES(claimESTaskInfoQueryVO);
        if (null != claimESTaskInfoResultVO) {
            log.info("claimRequestES-success:" + JSON.toJSONString(claimESTaskInfoResultVO));
            String responseCode = claimESTaskInfoResultVO.getResponseCode();
            if (StringUtils.isNotEmpty(responseCode) && "000000".equals(responseCode)) {
                ClaimESTaskInfoResultDataVO claimESTaskInfoResultDataVO = claimESTaskInfoResultVO.getData();
                if (null != claimESTaskInfoResultDataVO) {
                    claimESInfoVOList = claimESTaskInfoResultDataVO.getList();
                }
            } else {
                throw new GlobalBusinessException("理赔调用ES查询任务异常!");
            }
        } else {
            throw new GlobalBusinessException("理赔调用ES查询任务异常!");
        }
        return claimESInfoVOList;
    }

    /**
     * 请求ES
     * @param claimESTaskInfoQueryVO
     * @return
     */
    public ClaimESTaskInfoResultVO requestES(ClaimESTaskInfoQueryVO claimESTaskInfoQueryVO) {
        log.info("理赔调用ES, url: {}, params: {}", esUrl, JSON.toJSONString(claimESTaskInfoQueryVO));
        ClaimESTaskInfoResultVO claimESTaskInfoResultVO = new ClaimESTaskInfoResultVO();
        if (switchMesh){
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            String result = MeshSendUtils.post(esUrl, JSON.toJSONString(claimESTaskInfoQueryVO),headers);
            if (StringUtils.isNotEmpty(result)) {
                claimESTaskInfoResultVO = JSON.parseObject(result,ClaimESTaskInfoResultVO.class);
            } else {
                throw new GlobalBusinessException("理赔调用ES查询任务异常!");
            }
        }else {
            HttpHeaders header = new HttpHeaders();
            header.add("Content-Type", "application/json;charset:utf-8");
            HttpEntity<ClaimESTaskInfoQueryVO> httpEntity = new HttpEntity<>(claimESTaskInfoQueryVO, header);
            claimESTaskInfoResultVO = restTemplate.postForObject(esUrl, httpEntity, ClaimESTaskInfoResultVO.class);
        }
        log.info("理赔调用ES, result: {}", JSON.toJSONString(claimESTaskInfoResultVO));
        return claimESTaskInfoResultVO;
    }

    /**
     * 通过报案号修改案件信息给ES同步数据
     * @param reportNoList 报案号集合
     */
    public Map<String,String> modifyTaskInfoOnES (List<String> reportNoList){
        Map<String,String> resultMap = new HashMap<>();
        if (ListUtils.isNotEmpty(reportNoList)) {
            for (String reportNo : reportNoList) {
                taskInfoMapper.modifyTaskInfoOnES(reportNo);
            }
            resultMap.put("id",reportNoList.toString());
        }
        return resultMap;
    }

    /**
     * 全量同步理赔任务数据到ES
     */
    public Map<String,String> pushAllTaskInfoToES (String status,String updatedStarTDate,String updatedEndDate){
        taskInfoMapper.pushAllTaskInfoToES(status,updatedStarTDate,updatedEndDate);
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("id","同步完成");
        return resultMap;
    }

    /**
     * ES中理赔待处理任务数量监控
     */
    public void checkESTaskCount (){
        ClaimESTaskInfoQueryVO claimESTaskInfoQueryVO = new ClaimESTaskInfoQueryVO ();
        claimESTaskInfoQueryVO.setPage(1);
        claimESTaskInfoQueryVO.setRows(1);
        List<String> statuss = new ArrayList<>();
        statuss.add("0");
        statuss.add("3");
        claimESTaskInfoQueryVO.setStatuss(statuss);
        ClaimESTaskInfoResultVO claimESTaskInfoResultVO = this.requestES(claimESTaskInfoQueryVO);
        if (null != claimESTaskInfoResultVO) {
            log.info("claimRequestES-success:" + JSON.toJSONString(claimESTaskInfoResultVO));
            String responseCode = claimESTaskInfoResultVO.getResponseCode();
            if (StringUtils.isNotEmpty(responseCode) && "000000".equals(responseCode)) {
                ClaimESTaskInfoResultDataVO claimESTaskInfoResultDataVO = claimESTaskInfoResultVO.getData();
                if (null != claimESTaskInfoResultDataVO) {
                    CommonParameterEntity ccmmonParameterEntity = commonParameterMapper.getCommonParameterByCollectionCode("ES_SWITCH");
                    Integer total = claimESTaskInfoResultDataVO.getTotal();
                    List<TaskInfoDTO> taskInfoDTOList = taskInfoMapper.getUndealCaseList();
                    Integer undealCaseCount = 0;
                    if (CollectionUtils.isNotEmpty(taskInfoDTOList)) {
                        undealCaseCount =taskInfoDTOList.size();
                    }
                    if ((total-undealCaseCount) > alarmCount || (undealCaseCount-total) > alarmCount) {
                        if (null != ccmmonParameterEntity && "Y".equals(ccmmonParameterEntity.getValueCode())) {
                            redisTemplate.opsForValue().set("esSwitch","N");
                            ccmmonParameterEntity.setValueCode("N");
                            commonParameterMapper.updateByPrimaryKeySelective(ccmmonParameterEntity);
                        }
                        //查询差异
                        Map<String,Set<String>> diffListMap = new HashMap<>();
                        try {
                            diffListMap = queryESAndDBDiffList(taskInfoDTOList,total);
                        } catch (Exception e) {
                            LogUtil.error("获取差异清单失败！",e);
                        }
                        LogUtil.error("ES中理赔待处理任务数量监控：ES与数据库待处理任务数量不一致(ES:"+total+",数据库:"+undealCaseCount+")!");
                        throw new GlobalBusinessException("ES中理赔待处理任务数量监控：ES与数据库待处理任务数量不一致(ES:"+total+",数据库:"+undealCaseCount+")，差异清单："+JSON.toJSONString(diffListMap)+"!");
                    } else {
                        if (null != ccmmonParameterEntity && "N".equals(ccmmonParameterEntity.getValueCode())) {
                            redisTemplate.opsForValue().set("esSwitch","Y");
                            ccmmonParameterEntity.setValueCode("Y");
                            commonParameterMapper.updateByPrimaryKeySelective(ccmmonParameterEntity);
                        }
                    }
                } else {
                    LogUtil.error("ES中理赔待处理任务数量监控：调用ES未返回数据!");
                    throw new GlobalBusinessException("ES中理赔待处理任务数量监控：调用ES未返回数据!");
                }
            } else {
                LogUtil.error("ES中理赔待处理任务数量监控：调用ES异常！");
                throw new GlobalBusinessException("ES中理赔待处理任务数量监控：调用ES异常！");
            }
        } else {
            LogUtil.error("ES中理赔待处理任务数量监控：调用ES未返回数据!");
            throw new GlobalBusinessException("ES中理赔待处理任务数量监控：调用ES未返回数据!");
        }
    }

    /**
     * 获取ES查询开关
     * @return
     */
    public String getESQuerySwitch () {
       String esSwitch = esNacos;
       if(Constants.FLAG_Y.equals(esSwitch)){
           if (redisTemplate.hasKey("esSwitch")) {
               esSwitch = (String) redisTemplate.opsForValue().get("esSwitch");
           } else {
               CommonParameterEntity ccmmonParameterEntity = commonParameterMapper.getCommonParameterByCollectionCode("ES_SWITCH");
               if (null != ccmmonParameterEntity) {
                   esSwitch = ccmmonParameterEntity.getValueCode();
                   redisTemplate.opsForValue().set("esSwitch",esSwitch);
               }
           }
       }
       log.info("理赔调用ES开关: {}", esSwitch);
       return esSwitch;
    }

    /**
     *
     * ES获取理赔信息
     * @param idCase
     * @return 理赔信息
     */
    public ClaimInfoToESVO getClaimInfoToES(String idCase) {
        log.info("ES获取理赔信息入参: {}", idCase);
        ClaimInfoToESVO claimInfoToESVO =  this.taskInfoMapper.getClaimInfoToES(idCase);
        log.info("ES获取理赔信息出参: {},结果返回:{}", idCase,JsonUtils.toJsonString(claimInfoToESVO));
        return claimInfoToESVO;
    }

    /**
     * 查询ES和数据库之间的差异列表
     *
     * @return 包含差异列表的Map，键为报告编号，值为差异任务编号的集合
     */
    public Map<String,Set<String>> queryESAndDBDiffList (List<TaskInfoDTO> taskInfoDTOList,Integer total){
        //差异清单
        Map<String,Set<String>> diffListMap = new HashMap<>();
        ClaimESTaskInfoQueryVO claimESTaskInfoQueryVO = new ClaimESTaskInfoQueryVO ();
        claimESTaskInfoQueryVO.setPage(1);
        claimESTaskInfoQueryVO.setRows(1);
        List<String> statuss = new ArrayList<>();
        statuss.add("0");
        statuss.add("3");
        claimESTaskInfoQueryVO.setStatuss(statuss);
        ClaimESTaskInfoResultVO claimESTaskInfoResultVO  = new ClaimESTaskInfoResultVO();
        if (null == total) {
            claimESTaskInfoResultVO = this.requestES(claimESTaskInfoQueryVO);
        } else {
            claimESTaskInfoResultVO.setResponseCode("000000");
            claimESTaskInfoResultVO.setResponseMessage("成功");
            ClaimESTaskInfoResultDataVO data = new ClaimESTaskInfoResultDataVO();
            data.setTotal(total);
            data.setPages(total);
            data.setPageSize(1);
            claimESTaskInfoResultVO.setData(data);
        }
        if (null != claimESTaskInfoResultVO) {
            log.info("claimRequestES-success:" + JSON.toJSONString(claimESTaskInfoResultVO));
            String responseCode = claimESTaskInfoResultVO.getResponseCode();
            if (StringUtils.isNotEmpty(responseCode) && "000000".equals(responseCode)) {
                ClaimESTaskInfoResultDataVO claimESTaskInfoResultDataVO = claimESTaskInfoResultVO.getData();
                if (null != claimESTaskInfoResultDataVO) {
                    //获取全量数据
                    total = claimESTaskInfoResultDataVO.getTotal();
                    int pageSize = 9999; // 每页大小
                    int totalPages = (int) Math.ceil((double) total / pageSize); // 计算总页数
                    List<ClaimESTaskInfoVO> claimESTaskInfoVOlist = new ArrayList<>();
                    for (int currentPage = 1; currentPage <= totalPages; currentPage++) {
                        claimESTaskInfoQueryVO.setPage(currentPage);
                        claimESTaskInfoQueryVO.setRows(pageSize);
                        // 发起请求获取当前页的数据
                        ClaimESTaskInfoResultVO resultVO = this.requestES(claimESTaskInfoQueryVO);
                        ClaimESTaskInfoResultDataVO dataVO = resultVO.getData();
                        if (dataVO != null && CollectionUtils.isNotEmpty(dataVO.getList())) {
                            claimESTaskInfoVOlist.addAll(dataVO.getList());
                        }
                    }
                    //数据库待处理案件清单
                    if (null == taskInfoDTOList || taskInfoDTOList.isEmpty()) {
                        taskInfoDTOList = taskInfoMapper.getUndealCaseList();
                    }
                    if (CollectionUtils.isNotEmpty(taskInfoDTOList) && CollectionUtils.isNotEmpty(claimESTaskInfoVOlist)) {
                        //ES和数据库对比差异清单
                        Map<String, Set<String>> diffListMapEsToDb = new HashMap<>();
                        Map<String, Set<String>> diffListMapDbToEs = new HashMap<>();
                        for (ClaimESTaskInfoVO claimESTaskInfoVO : claimESTaskInfoVOlist) {
                            String idAhcsTaskInfo = claimESTaskInfoVO.getIdAhcsTaskInfo();
                            boolean found = taskInfoDTOList.stream()
                                    .anyMatch(taskInfoDTO -> Objects.equals(idAhcsTaskInfo, taskInfoDTO.getIdAhcsTaskInfo()));
                            if (!found) {
                                diffListMapEsToDb.computeIfAbsent(claimESTaskInfoVO.getReportNo(), k -> new HashSet<>()).add(idAhcsTaskInfo);
                            }
                        }
                        //数据库和ES对比差异清单
                        for (TaskInfoDTO taskInfoDTO : taskInfoDTOList) {
                            String idAhcsTaskInfo = taskInfoDTO.getIdAhcsTaskInfo();
                            boolean found = claimESTaskInfoVOlist.stream()
                                    .anyMatch(claimESTaskInfoVO -> Objects.equals(idAhcsTaskInfo, claimESTaskInfoVO.getIdAhcsTaskInfo()));
                            if (!found) {
                                diffListMapDbToEs.computeIfAbsent(taskInfoDTO.getReportNo(), k -> new HashSet<>()).add(idAhcsTaskInfo);
                            }
                        }
                        // 合并结果
                        diffListMap.putAll(diffListMapEsToDb);
                        diffListMap.putAll(diffListMapDbToEs.entrySet().stream()
                            .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                e -> e.getValue().stream()
                                        .filter(id -> !diffListMapEsToDb.getOrDefault(e.getKey(), Collections.emptySet()).contains(id))
                                        .collect(Collectors.toSet())
                            )));
                    }
                    //根据配置开关，自动同步ES和数据库的差异
                    if(Constants.FLAG_Y.equals(diffAutoSyncFlag)){
                        diffListMap.forEach((reportNo, idAhcsTaskInfoSet) -> {
                            taskInfoMapper.modifyTaskInfoOnES(reportNo);
                        });
                    }
                } else {
                    LogUtil.error("ES中理赔待处理任务差异清单对比：调用ES未返回数据!");
                }
            } else {
                LogUtil.error("ES中理赔待处理任务差异清单对比：调用ES异常！");
            }
        } else {
            LogUtil.error("ES中理赔待处理任务差异清单对比：调用ES未返回数据!");
        }
        log.info("ES中理赔待处理任务差异清单=result: {}", JSON.toJSONString(diffListMap));
        return diffListMap;
    }

    public GlobalTaskRespVO workbenchGlobalQuery() throws Exception{
        String userId = WebServletContext.getUserId();
        if(StringUtils.isEmptyStr(userId)){
            throw new Exception("登录用户为空");
        }
        GlobalTaskRespVO globalTaskRespVO = new GlobalTaskRespVO();
        int total = 0;
        Map<String, Object> taskList = new HashMap<>();
        Map<String, Integer> workCount = new HashMap<>();
        String dealResult = globalDealQuery(userId, "");
        JSONObject jsonObject = JSON.parseObject(dealResult);
        String resultCode = jsonObject.getString("resultCode");
        if("0000".equals(resultCode)){
            List<GlobalDealVO> listData = jsonObject.getObject("listData", new com.alibaba.fastjson.TypeReference<List<GlobalDealVO>>() {});
            Map<String, List<GlobalDealVO>> collectDeal = listData.stream().collect(Collectors.groupingBy(GlobalDealVO::getStatusParam));
            List<GlobalDealVO> globalDeal01 = collectDeal.get("01");
            if(!CollectionUtils.isEmpty(globalDeal01)){
                workCount.put("D01", globalDeal01.size());
                total = total + globalDeal01.size();
                taskList.put("D01", globalDeal01);
            }

            List<GlobalDealVO> globalDeal02 = collectDeal.get("02");
            if(!CollectionUtils.isEmpty(globalDeal02)){
                workCount.put("D02", globalDeal02.size());
                total = total + globalDeal02.size();
                taskList.put("D02", globalDeal02);
            }

            List<GlobalDealVO> globalDeal04 = collectDeal.get("04");
            if(!CollectionUtils.isEmpty(globalDeal04)){
                workCount.put("D04", globalDeal04.size());
                total = total + globalDeal04.size();
                taskList.put("D04", globalDeal04);
            }
        }

//        List<String> listStatus = Arrays.asList("01", "02", "04");
//        for (String status : listStatus) {
//            String dealResult = globalDealQuery(userId, status);
//            JSONObject jsonObject = JSON.parseObject(dealResult);
//            String resultCode = jsonObject.getString("resultCode");
//            if ("0000".equals(resultCode)) {
//                List<GlobalDealVO> listData = jsonObject.getObject("listData", new com.alibaba.fastjson.TypeReference<List<GlobalDealVO>>() {
//                });
//                if (!CollectionUtils.isEmpty(listData)) {
//                    String name = "D" + status;
//                    workCount.put(name, listData.size());
//                    total = total + listData.size();
//                    taskList.put(name, listData);
//                }
//            }
//        }
        globalTaskRespVO.setWorkCount(workCount);

        Map<String, Integer> auditCount = new HashMap<>();
        for (GlobalNodeStatus globalNodeStatus : GlobalNodeStatus.values()) {
            String auditResult = globalAuditQuery(userId, globalNodeStatus.getCode());
            JSONObject jsonObjectAudit = JSON.parseObject(auditResult);
            String resultCodeAudit = jsonObjectAudit.getString("resultCode");
            if("0000".equals(resultCodeAudit)){
                List<GlobalAuditVO> listData = jsonObjectAudit.getObject("listData", new com.alibaba.fastjson.TypeReference<List<GlobalAuditVO>>() {});
                if(!CollectionUtils.isEmpty(listData)){
                    auditCount.put(globalNodeStatus.getCode(), listData.size());
                    total = total + listData.size();
                    taskList.put(globalNodeStatus.getCode(), listData);
                }
            }

        }
        globalTaskRespVO.setAuditCount(auditCount);
//        String auditResult = globalAuditQuery(userId);
//        JSONObject jsonObjectAudit = JSON.parseObject(auditResult);
//        String resultCodeAudit = jsonObjectAudit.getString("resultCode");
//        if("0000".equals(resultCodeAudit)){
//            Map<String, Integer> workCount = new HashMap<>();
//            List<GlobalAuditVO> listData = jsonObject.getObject("listData", new com.alibaba.fastjson.TypeReference<List<GlobalAuditVO>>() {});
//            Map<String, List<GlobalDealVO>> collectDeal = listData.stream().collect(Collectors.groupingBy(GlobalDealVO::getStatusParam));
//            List<GlobalDealVO> globalDeal01 = collectDeal.get("01");
//            if(!CollectionUtils.isEmpty(globalDeal01)){
//                workCount.put("未立案", globalDeal01.size());
//                total = total + globalDeal01.size();
//                taskList.put("未立案", globalDeal01);
//            }
//
//            List<GlobalDealVO> globalDeal02 = collectDeal.get("02");
//            if(!CollectionUtils.isEmpty(globalDeal02)){
//                workCount.put("已立案未决", globalDeal02.size());
//                total = total + globalDeal02.size();
//                taskList.put("已立案未决", globalDeal02);
//            }
//
//            List<GlobalDealVO> globalDeal03 = collectDeal.get("03");
//            if(!CollectionUtils.isEmpty(globalDeal03)){
//                workCount.put("已立案未估损", globalDeal03.size());
//                total = total + globalDeal03.size();
//                taskList.put("已立案未估损", globalDeal03);
//            }
//            globalTaskRespVO.setWorkCount(workCount);
//        }

        globalTaskRespVO.setTotal(total);
        globalTaskRespVO.setTaskList(taskList);
        return globalTaskRespVO;
    }

    private String globalDealQuery(String userId, String status) throws Exception {
        Map<String, Object> paramMap = new HashMap<>(3);
        paramMap.put("class", "sfmi.common.page.PageList");

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("class", "java.util.HashMap");
        queryMap.put("fromPeriod", "00010101");
        queryMap.put("toPeriod", DateUtils.dateFormat(new Date(), DateUtils.DATE_FORMAT_YYYYMMDD));
        queryMap.put("clerkNo", userId);
//        queryMap.put("clerkNo", "shayuting");
        queryMap.put("status", status);
        paramMap.put("condition", queryMap);

        Map<String, Object> pageMap = new HashMap<>();
        pageMap.put("class", "sfmi.common.page.PageInfo");
        pageMap.put("currentPage", 1);
        pageMap.put("rowCntPerPage", 999);
        paramMap.put("pageInfo", pageMap);

        log.info("工作台调用global案件待处理接口，url：{}，userId：{}", globalDealUrl, userId);
        String result = HttpClientUtil.doPost(globalDealUrl, JsonUtils.toJsonString(paramMap));
        log.info("工作台调用global案件待处理接口，result：{}", result);

        return result;

    }

    private String globalAuditQuery(String userId, String statusCode) throws Exception {
        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("class", "java.util.HashMap");
        queryMap.put("apprStartDate", "00010101");
        queryMap.put("apprEndDate", DateUtils.dateFormat(new Date(), DateUtils.DATE_FORMAT_YYYYMMDD));
        queryMap.put("apprClerkNo", userId);
        queryMap.put("schApprCls", "1");
//        queryMap.put("apprClerkNo", "shayuting");
        queryMap.put("schReportingCls", "A");
        queryMap.put("apprCls", "1");
        queryMap.put("currentPage", 1);
        queryMap.put("rowCntPerPage", 999);

        queryMap.put("procCls", statusCode);

        log.info("工作台调用global案件待审核接口，url：{}，userId：{}", globalAuditUrl, userId);
        String result = HttpClientUtil.doPost(globalAuditUrl, JsonUtils.toJsonString(queryMap));
        log.info("工作台调用global案件待审核接口，result：{}", result);

        return result;

    }

    public String jumpToGlobal(String reportNo) throws Exception {
        String userId = WebServletContext.getUserId();
        String encrypt = WeiBaoUtil.encrypt(userId, globalAesKey);
//        String encrypt = WeiBaoUtil.encrypt("shayuting", globalAesKey);
        return String.format(globalJumpUrl, encrypt, reportNo);
    }

    public GlobalWorkResVo workbenchGlobalCount(GlobalWorkReqVo globalWorkReqVo) {
        //入参处理
        String json = JsonUtils.toJsonString(globalWorkReqVo);
        //调用global接口查询工作指标数据
        String res = openGlobalService.openGloabl(countUrl,json);
        GlobalWorkResVo globalWorkResVo = new GlobalWorkResVo();
        if(res!=null){
            globalWorkResVo = JSONObject.parseObject(res,GlobalWorkResVo.class);
        }
        globalWorkResVo.getResponseData().setResponseTime(SimpleDateFormat.getDateInstance().format(new Date()));
        if(!Objects.isNull(globalWorkResVo.getResponseData().getClaimCountInfo())){
            globalWorkResVo.getResponseData().getClaimCountInfo().setSystemCode("global");
        }
        return globalWorkResVo;
    }
}

