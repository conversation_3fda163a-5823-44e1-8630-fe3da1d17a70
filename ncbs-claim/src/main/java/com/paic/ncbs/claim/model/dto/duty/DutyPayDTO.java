package com.paic.ncbs.claim.model.dto.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.constant.ModelConsts;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO;
import com.paic.ncbs.claim.model.vo.settle.DutyBillLimitInfoVO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("险种责任赔付信息")
public class DutyPayDTO extends EntityDTO {

    private static final long serialVersionUID = 1262397583511962490L;
    @ApiModelProperty("CLMS_DUTY_PAY主键")
    private String idAhcsDutyPay;
    @ApiModelProperty("clm_plan_duty_pay主键")
    private String idAhcsPlanPay;
    @ApiModelProperty("CLMS_POLICY_DUTY责任表主键")
    private String idCopyDuty;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("赔案号")
    private String caseNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("赔付批次号")
    private String idAhcsBatch;
    @ApiModelProperty("险种代码")
    private String planCode;
    @ApiModelProperty("责任编码")
    private String dutyCode;
    @ApiModelProperty("责任名称")
    private String dutyName;
    @ApiModelProperty("责任描述")
    private String dutyDesc;
    @ApiModelProperty("理算金额")
    private BigDecimal settleAmount;
    @ApiModelProperty("已赔款金额")
    private BigDecimal paidAmount;
    @ApiModelProperty("基本保额")
    private BigDecimal baseAmountPay;
    @ApiModelProperty("最大给付额")
    private BigDecimal maxAmountPay = new BigDecimal(0);
    @ApiModelProperty("免赔额")
    private BigDecimal remitAmount;
    @ApiModelProperty("免赔额类型： 免赔额类型：0-次免赔，1-年免赔,2-日免赔")
    private String remitAmountType;
    @ApiModelProperty("免赔天数")
    private BigDecimal remitDays;
    @ApiModelProperty("理算依据")
    private String settleReason;
    @ApiModelProperty("仲裁费")
    private BigDecimal arbitrageFee;
    @ApiModelProperty("诉讼费")
    private BigDecimal lawsuitFee;
    @ApiModelProperty("公估费")
    private BigDecimal commonEstimateFee;
    @ApiModelProperty("律师费")
    private BigDecimal lawyerFee;
    @ApiModelProperty("执行费")
    private BigDecimal executeFee;
    @ApiModelProperty("检验费")
    private BigDecimal verifyAppraiseFee;
    @ApiModelProperty("调查费")
    private BigDecimal inquireFee;
    @ApiModelProperty("其他")
    private BigDecimal otherFee;
    @ApiModelProperty("专项查勘费")
    private BigDecimal specialSurveyFee;
    @ApiModelProperty("费用总额")
    private BigDecimal feeSum;
    @ApiModelProperty("赔付类型")
    private String claimType = ModelConsts.CLAIM_TYPE_PAY;
    @ApiModelProperty("责任明细赔付信息")
    private List<DutyDetailPayDTO> dutyDetailPayArr;
    @ApiModelProperty("归档时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveTime;
    @ApiModelProperty("展示颜色")
    private String disPlayColor;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("共保基本保额")
    private BigDecimal shareDutyBaseAmount;
    @ApiModelProperty("是否责任共享保额")
    private Boolean isDutyShareAmount = false;
    @ApiModelProperty("责任共享保额责任编码")
    private String shareDutyGroup;
    @ApiModelProperty("是否标的共享保额")
    private Boolean isShareAmount = false;
    @ApiModelProperty("共享保额代码")
    private String groupCode;
    @ApiModelProperty("其他责任属性列表")
    private List<DutyAttributeDTO> attributes;
    private Integer subTimes ;
    @ApiModelProperty("已预赔金额")
    private BigDecimal preAmount;

    private List<DutyBillLimitInfoVO>  dutyLimitPayList;

    /**
     * 保单起期
     */
    @ApiModelProperty("责任层保单开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceBeginDate;
    /***
     * 保单止期
     */
    @ApiModelProperty("责任层保单止期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceEndDate;

    @ApiModelProperty(name = "赔偿限额")
    private BigDecimal payLimit;
    private String idPlyRiskProperty;
    /**
     * 被保险人社保标识 2-社保  0或null 为非社保
     */
    private String isSociaSecurity;

    @ApiModelProperty("方案编码")
    private String productPackage;
    /**
     * 责任明细是否理算标志：Y-是，N-否
     * 核责打标，标记哪些责任明细是可以理算
     * 为N的理算金额直接为0 理算依据为空
     */
    private String isSettleFlag;

    /**
     * 公式
     */
    private  String formula;

    /**
     * 责任年免赔额
     */
    private CalculateAmountService yearRemitAmountServiceImpl;
    /**
     * 责任年次赔额
     */
    private CalculateAmountService timesRemitAmountServiceImpl;

    @ApiModelProperty("责任赔付金额变化量")
    private BigDecimal chgDutyPayAmount;

    public void setShareDutyGroup(String shareDutyGroup){
        this.shareDutyGroup = shareDutyGroup;
        if (StringUtils.isNotEmpty(shareDutyGroup)){
            this.isDutyShareAmount = true;
        }
    }

    public void setSettleAmount(BigDecimal settleAmount){
        if (settleAmount==null){
            this.settleAmount = null;
        }else{
            this.settleAmount = settleAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }

    public List<DutyDetailPayDTO> getDutyDetailPayArr(){
        return this.dutyDetailPayArr == null ? new ArrayList<>() : this.dutyDetailPayArr;
    }

    public static BigDecimal sum(BigDecimal... bds) {
        if (bds == null) {
            return new BigDecimal("0.00");
        }
        BigDecimal sum = new BigDecimal("0.00");
        for (BigDecimal bigDecimal : bds) {
            sum = sum.add(nvl(bigDecimal, 0));
        }
        return sum;
    }

    public static BigDecimal nvl(BigDecimal bd, int val) {
        if (bd == null) {
            return new BigDecimal(val);
        }
        return bd;
    }


}
