package com.paic.ncbs.claim.common.constant;


import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


public class ConfigConstValues {

    public static final String YES = "Y";

    public static final String NO = "N";

    // 顶级机构
    public static final String HQ_DEPARTMENT = "1";

    public static final Integer DEPT_LEVEL_TWO = 2;

    public static final Integer DEPT_LEVEL_THREE = 3;

    /**
     * 01-待立案
     */
    public static final String PROCESS_STATUS_PENDING_REGISTRATION = "01";

    /**
     * 02-待收单
     */
    public static final String PROCESS_STATUS_PENDING_ACCEPT = "02";

    /**
     * 03-待审核
     */
    public static final String PROCESS_STATUS_PENDING_AUDIT = "03";

    /**
     * 04-审核中
     */
    public static final String PROCESS_STATUS_AUDITTING = "04";

    /**
     * 05-待结案
     */
    public static final String PROCESS_STATUS_PENDING_CASE = "05";

    /**
     * 06-已结案
     */
    public static final String PROCESS_STATUS_CASE_CLOSED = "06";

    /**
     * 07-零结
     */
    public static final String PROCESS_STATUS_ZORE_CANCEL = "07";

    /**
     * 08-注销
     */
    public static final String PROCESS_STATUS_CANCELLATION = "08";

    public static final String PROCESS_STATUS_WAIT_CONFIRM = "10";


    public static final List<String> PROCESS_STATUS_LIST_UNCKECKED = new ArrayList<>();
    static {
        PROCESS_STATUS_LIST_UNCKECKED.add(PROCESS_STATUS_PENDING_REGISTRATION);
        PROCESS_STATUS_LIST_UNCKECKED.add(PROCESS_STATUS_PENDING_ACCEPT);
        PROCESS_STATUS_LIST_UNCKECKED.add(PROCESS_STATUS_PENDING_AUDIT);
    }

    public static final String PROCESS_STATUS_PENDING_ACCEPT_NAME = "待收单";

    public static final String PROCESS_STATUS_CASE_CLOSED_NAME = "已结案";

    public static final String KEY_REPORT_NO = "reportNo";
    public static final String KEY_CASE_TIMES = "caseTimes";
    public static final String KEY_UPDATEBY = "updateBy";
    public static final String KEY_ESTIMATE_TYPE = "estimateType";

    /**
     * 正常
     */
    public static final String INDEMNITYCONCLUSION_PAY = "1";
    /**
     * 拒赔
     */
    public static final String INDEMNITYCONCLUSION_REFUSE = "4";
    /**
     * 零结
     */
    public static final String INDEMNITYCONCLUSION_ZERO_CLOSED = "2";
    /**
     * 立案注销
     */
    public static final String INDEMNITYCONCLUSION_CANCEL = "5";
    /**
     * 报案注销
     */
    public static final String INDEMNITYCONCLUSION_CANCEL_REPORT = "6";
    public static final String INDEMNITYMODEL_ACCOMMODATION = "6";
    public static final String INDEMNITYMODEL_PROTOCOL = "5";

    public static final String CASE_BASE_STATUS_PENDING_CASE = "5";


    public static final String CASE_BASE_STATUS_AUDITTING = "4";

    public static final String PROCESS_STATUS_05 = "待结案";

    public static final String PROCESS_STATUS_06 = "已结案";

    public static final String PROCESS_STATUS_07 = "零结案";

    public static final String PROCESS_STATUS_08 = "已注销";

    public static final Set<String> PROCESS_STATUS_END_SET = new HashSet<>();

    static {
        PROCESS_STATUS_END_SET.add(PROCESS_STATUS_05);
        PROCESS_STATUS_END_SET.add(PROCESS_STATUS_06);
        PROCESS_STATUS_END_SET.add(PROCESS_STATUS_07);
        PROCESS_STATUS_END_SET.add(PROCESS_STATUS_08);
    }

    public static final String WHOLE_CASE_STATUS_END = "0";

    public static final String PRINT_DOCTYPE_PROTOCOL_PAY = "02";
    public static final String PRINT_DOCTYPE_ZERO_END = "05";
    public static final String PRINT_DOCTYPE_CANCEL_END = "06";
    public static final String PRINT_DOCTYPE_FORMAL_PAY = "01";
    public static final String PRINT_DOCTYPE_ACCOMMODATION_PAY = "03";
    public static final String PRINT_DOCTYPE_REFUSE_PAY = "04";

    public static final String AUDIT_PROCESSING = "1";

    public static final String AUDIT_END = "2";

    public static final String AUDIT_NOTNEED = "3";



}
