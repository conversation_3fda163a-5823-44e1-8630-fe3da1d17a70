package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.*;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MapperScan
public interface MedicalBillInfoMapper extends BaseDao<MedicalBillInfoDTO> {

     void addBillInfo(MedicalBillInfoVO medicalBillInfo);

    void addBillInfoAmend(MedicalBillInfoVO medicalBillInfo);

     void modifyBillInfo(MedicalBillInfoVO medicalBillInfoVO);

     List<MedicalBillInfoDTO> getBillInfoByPage(MedicalBillInfoDTO medicalBillInfoDTO);

     MedicalBillInfoVO getBillInfo(String idAhcsBillInfo);

     void removeBillInfoList(@Param("idAhcsBillInfoList") List<String> idAhcsBillInfoList,
                                   @Param("userUM") String userUM);

     List<MedicalBillAmountVO> getMedicalBillAmount(MedicalBillAmountVO medicalBillAmountVO);

     CompareBillNoResultVO validBillExist(MedicalBillInfoVO medicalBillInfoVO);

     List<MedicalBillInfoVO> getMedicalBillInfoForPrint(@Param("reportNo") String reportNo,
                                                              @Param("caseTimes") Integer caseTimes);

     String getMedicalBillCreatedBy(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     Integer getBillInfoCount(MedicalBillInfoDTO medicalBillInfoDTO);

     MedicalBillInfoDTO getMedicalBillAllAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("lossObjectNo") String lossObjectNo);

     MedicalBillInfoDTO getMedicalBillTotal(@Param("reportNo") String reportNo,
                                                  @Param("caseTimes") Integer caseTimes);

     void clearBillInfoAmonut(@Param("userId") String userId, @Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("lossObjectNo") String lossObjectNo);

     List<String> getIdBillInfoList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<String> getBillClassByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     MedicalBillInfoPageVO getMedicalBillAllSumAmount(MedicalBillInfoDTO medicalBillInfoDTO);

     List<CompareBillNoResultVO> getSameBillNoReportNo(MedicalBillInfoVO medicalBillInfoVO);

    List<String> getSameBillOnlyReportNo(MedicalBillInfoVO medicalBillInfoVO);

     void addBillInfoList(List<MedicalBillInfoVO> billInfos);

     List<CompareBillNoResultVO> getOtherCaseSameBillNo(@Param("reportNo") String reportNo, @Param("lossObjectNo") String lossObjectNo);

     MedicalBillInfoDTO getMedicalBillInfoByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     BigDecimal getPreInsuranceTotalPayment(MedicalBillInfoVO medicalBillInfoVO);

     void updateInsuranceTotalPayment(@Param("idAhcsBillInfoList") List<String> idAhcsBillInfoList,
                                            @Param("userUM") String userUM, @Param("insuranceTotalPayment") BigDecimal insuranceTotalPayment);

     String getTargetBillId(MedicalBillInfoVO medicalBillInfoVO);

     BigDecimal getPreInsuranceTotalPaymentById(MedicalBillInfoVO medicalBillInfoVO);

     BigDecimal getSufPrepaidAmountById(MedicalBillInfoVO medicalBillInfoVO);

     List<String> getBillIdForUpdateInnsuranceTotal(MedicalBillInfoVO medicalBillInfoVO);

    /**
     * 案件重开，数据拷贝
     * @param paramList
     */
    void copyForCaseReopen(List<BillCopyDTO> paramList);

    /**
     * 诊断信息查询
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<PersonDiagnoseDTO> getPersonDiagnoseList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,@Param("taskId") String taskId);

    /**
     * 保存诊断医院发票关联信息
     * @param dto
     */
   // void saveDiagnoseHospitalBillAssociation(DiagnoseHospitalBillAssociationDTO dto);

    /**
     * 根据idAhcsBillInfo将历史数据置为失效状态
     * @param idAhcsBillInfo
     */
   // void updateDiagnoseHospitalBillAssociation(String idAhcsBillInfo);

    /**
     * 根据idAhcsBillInfo查询诊断信息
     * @param
     * @return
     */
   // List<DiagnoseHospitalBillAssociationDTO> getDiagnoseHospitalBillAssociation(String idAhcsBillInfo);

    List<MedicalBillInfoVO> getIsSocBillInfoList(String reportNo,Integer caseTimes,List<String> list);

    /**
     * 查询没有经医保结算的发票信息
     * @param reportNo
     * @param caseTimes
     * @param list
     * @return
     */
    List<MedicalBillInfoVO> getNoSocBillInfoList(String reportNo, int caseTimes, List<String> list);

    List<MedicalBillInfoVO> getBillInfoGroupDateList(String reportNo,Integer caseTimes);

    /**
     * 查询保单历史报案发票信息
     * @param policyNo
     * @return
     */
    List<MedicalBillHistoryVo> getPolicyHistoryBillInfo(String policyNo);

    /**
     * 查询报案号下的发票日期
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<MedicalBillInfoVO> getBillDateList(String reportNo,Integer caseTimes);

    List<MedicalBillInfoVO>  getAllBillInfo(String reportNo,Integer caseTimes);

    List<String> getBIllNo(String reportNo, Integer caseTimes, Date insuranceBeginDate, Date waitendDate);

    List<MedicalBillInfoVO>  getThisAllBillDate(String reportNo,Integer caseTimes);

    List<MedicalBillInfoVO>  getOtherAllBillDate(@Param("clientNo") String clientNo,
                                                 @Param("reportNo") String reportNo);

    List<MedicalBillInfoVO> getOtherBillDateByClientInfo(CaseRiskPropertyDTO caseRiskPropertyDTO);

}
