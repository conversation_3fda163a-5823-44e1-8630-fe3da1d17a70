package com.paic.ncbs.claim.dao.entity.reinsurance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * @description: 再保账单生成表
 * @date: 2025/08/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("clms_reins_bill")
public class ReinsBillDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;
    /**
     * 单证Id
     */
    @TableField(value = "document_group_items_id")
    private String documentGroupItemsId;
    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;
    /**
     * 案件号
     */
    @TableField(value = "case_no")
    private String caseNo;
    /**
     * 保单号
     */
    @TableField(value = "policy_no")
    private String policyNo;
    /**
     * 报案次数
     */
    @TableField(value = "case_times")
    private Integer caseTimes;
    /**
     * 编号
     */
    @TableField(value = "bill_no")
    private Integer billNo;

    /**
     * 账单类型 1-估损,2-实赔
     */
    @TableField(value = "bill_type")
    private String billType;
    /**
     * 账单查询再保时间
     */
    @TableField(value = "create_date")
    private Date createDate;
    /**
     * 环节类型1-立案,2-预赔，3-实赔，4-追偿、5-估损调整、7-结案、注销
     */
    @TableField(value = "claim_type")
    private String claimType;
    /**
     * 立案/估损/理算提交人
     */
    @TableField(value = "submit_code")
    private String submitCode;
    /**
     * 再保人编码
     */
    @TableField(value = "reins_code")
    private String reinsCode;
    /**
     * 再保人名称
     */
    @TableField(value = "reins_name")
    private String reinsName;
    /**
     * 再保人英文名
     */
    @TableField(value = "reins_Ename")
    private String reinsEName;
    /**
     * 分摊比例
     */
    @TableField(value = "share_rate")
    private BigDecimal shareRate;
    /**
     * 币别
     */
    @TableField(value = "currency")
    private String currency;
    /**
     * 总金额
     */
    @TableField(value = "sum_loss")
    private BigDecimal sumLoss;
    /**
     * 分摊金额
     */
    @TableField(value = "paid_loss")
    private BigDecimal paidLoss;
    /**
     * 状态1-上传单证成功，2-上传单证失败
     */
    @TableField(value = "status")
    private String status;
    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;
}
