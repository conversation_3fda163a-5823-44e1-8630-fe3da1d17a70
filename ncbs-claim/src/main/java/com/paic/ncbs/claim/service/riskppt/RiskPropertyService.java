package com.paic.ncbs.claim.service.riskppt;

import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO;
import com.paic.ncbs.claim.model.dto.riskppt.*;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;
import java.util.Map;

public interface RiskPropertyService {

    List<PolicyGroupDTO> buildPlyGroupDTO(List<CaseRiskPropertyDTO> riskPropertyList,List<CaseRiskPropertyDTO> selectedPropertyList);

    /**
     * 查询保单标的列表
     * @param queryDTO
     * @return
     */
    List<PolicyGroupDTO> getPlyRiskProperty(PlyRiskGroupQueryDTO queryDTO);

    /**
     * 查询案件标的列表
     * @param caseDTO
     * @return
     */
    List<PolicyGroupDTO> getCaseRiskProperty(CaseRiskPropertyDTO caseDTO);

    /**
     * 保存案件标的列表
     * @param riskDomainDTO
     * @return
     */
    void saveCaseRiskPropertyList(RiskDomainDTO riskDomainDTO);

    /**
     * 设置立案标的险种
     * @param estimatePolicyList
     */
    void setEstRiskPropertyPlan(List<EstimatePolicyDTO> estimatePolicyList);

    /**
     * 设置立案标的险种
     * @param estimatePolicyList
     * @param reportRiskList
     */
    void setEstRiskProperty(List<EstimatePolicyDTO> estimatePolicyList,List<CaseRiskPropertyDTO> reportRiskList);

    void setEstimateRiskGroup(List<EstimatePolicyDTO> estimatePolicyList);

    /**
     * 查询报案标的列表
     * @param caseDTO
     * @return
     */
    List<CaseRiskPropertyDTO> getReportRiskPropertyList(CaseRiskPropertyDTO caseDTO);

    /**
     * 查询案件标的列表
     * @param caseDTO
     * @return
     */
    List<CaseRiskPropertyDTO> getCaseRiskPropertyList(CaseRiskPropertyDTO caseDTO);

    /**
     * 校验标的类型
     * @param policyNos
     */
    void checkTargetType(List<String> policyNos);

    /**
     * 查询标的
     * @param reportNo
     * @param policyGroups
     * @param reportRiskDTOList
     * @return
     */
    Map<String,CaseRiskPropertyDTO> getReportRiskPropertyMap(String reportNo, List<PolicyGroupDTO> policyGroups, List<ReportRiskPropertyDTO> reportRiskDTOList);

    /**
     * 匹配标的
     * @param riskDomainDTO
     * @param reportRiskMap
     * @param selectedCaseRiskList
     * @return
     */
    List<CaseRiskPropertyDTO> matchCaseRiskProperty(RiskDomainDTO riskDomainDTO,Map<String,CaseRiskPropertyDTO> reportRiskMap,List<CaseRiskPropertyDTO> selectedCaseRiskList);

    /**
     * 设置理算标的险种
     * @param policyPayList
     */
    void setRiskPropertyPlan(List<PolicyPayDTO> policyPayList);

    /**
     * 设置理算标的险种
     * @param policyPayList
     */
    void setRiskProperty(List<PolicyPayDTO> policyPayList);

    void setRiskGroup(List<PolicyPayDTO> policyPays);

    /**
     * 设置标的id
     * @param idPlyRiskProperty
     * @param riskPlans
     */
    void setPlanDutyDetailRiskId(String idPlyRiskProperty,List<PlanPayDTO> riskPlans);

    /**
     * 根据标的id查询立案险种
     * @param riskPropertyDTO
     * @return
     */
    List<EstimatePolicyDTO> getEstPolicyByRiskPropertyId(CaseRiskPropertyDTO riskPropertyDTO);

    /**
     * 获取立案标的险种
     * @param estimatePolicyList
     */
    void getEstRiskPropertyPlan(List<EstimatePolicyDTO> estimatePolicyList);

    /**
     * 获取标的险种
     * @param policyPayList
     */
    void getRiskPropertyPlan(List<PolicyPayDTO> policyPayList);

    /**
     * 是否责任险
     * @param targetType
     * @param productClass
     * @return
     */
    boolean isRiskProperty(String targetType, String productClass);

    /**
     * 是否展示标的
     * @param reportNo
     * @param policyNo
     * @return
     */
    boolean displayRiskProperty(String reportNo, String policyNo);

    /**
     * 保存案件标的物
     * @param caseRiskProperty
     * @return
     */
    void saveCaseRiskProperty(CaseRiskPropertyDTO caseRiskProperty);

    /**
     * 保存案件标的
     * @param caseRiskPropertyList
     */
    void saveCaseRiskPropertyList(List<CaseRiskPropertyDTO> caseRiskPropertyList);

    /**
     * 逻辑删除选择标的
     * @param caseRiskPropertyDTO
     */
    void removeCaseRiskProperty(CaseRiskPropertyDTO caseRiskPropertyDTO);

    void deleteCaseRiskProperty(String reportNo, int caseTimes, String taskId);

    /**
     * 是否雇主责任险
     *
     * @param reportNo
     * @param policyNo
     * @return
     */
    boolean isEmployerInsurance(String reportNo, String policyNo);

    /**
     * 判断是否雇主责任险 true-是
     *
     * @param reportNo
     * @param policyNo
     * @return
     */
    boolean isEmployerInsuranceNew(String reportNo, String policyNo);

    /**
     *
     * @param caseDTO
     * @return
     */
    List<CaseRiskPropertyDTO> getLastTaskIdCaseRiskPropertyList(CaseRiskPropertyDTO caseDTO);


    /**
     * 更新标的任务节点
     * @param caseRiskPropertyDTO
     */
    void updateReportRiskPropertyTaskId(CaseRiskPropertyDTO caseRiskPropertyDTO);

    String getCaseRiskPropertyLastTaskId(CaseRiskPropertyDTO caseDTO);

    //判断是否标的保单
    boolean isRiskPropertyByPlyNo(String policyNo);

    //获取riskgroupNo
    ProductInfoDTO getRropertyProduct(String policyNo);

    void dealRiskProperty(RiskDomainDTO riskDomainDTO);
}
