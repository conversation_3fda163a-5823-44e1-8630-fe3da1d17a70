<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO" id="result">
        <id property="idAhcsDutyPay" column="ID_AHCS_DUTY_PAY"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="idAhcsPlanPay" column="ID_AHCS_PLAN_PAY"/>
        <result property="idCopyDuty" column="ID_AHCS_POLICY_DUTY"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
        <result property="paidAmount" column="PAID_AMOUNT"/>
        <result property="baseAmountPay" column="BASE_AMOUNT_PAY"/>
        <result property="settleReason" column="SETTLE_REASON"/>
        <result property="arbitrageFee" column="ARBITRAGE_FEE"/>
        <result property="lawsuitFee" column="LAWSUIT_FEE"/>
        <result property="commonEstimateFee" column="COMMON_ESTIMATE_FEE"/>
        <result property="lawyerFee" column="LAWYER_FEE"/>
        <result property="executeFee" column="EXECUTE_FEE"/>
        <result property="verifyAppraiseFee" column="VERIFY_APPRAISE_FEE"/>
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="isShareAmount" column="IS_SHARE_AMOUNT"/>
        <result property="shareDutyGroup" column="SHARE_DUTY_GROUP"/>
        <result property="insuranceBeginDate" column="insurance_begin_date"/>
        <result property="insuranceEndDate" column="insurance_end_date"/>
        <result property="idPlyRiskProperty" column="id_ply_risk_property"/>
        <result property="preAmount" column="preAmount"/>

        <!-- 关联责任 明细-->
        <collection property="dutyDetailPayArr"
                    ofType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO"
                    select="com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper.selectByAhcsDutyPayId"
                    column="{idAhcsDutyPay = ID_AHCS_DUTY_PAY,idCopyDuty=ID_AHCS_POLICY_DUTY }">
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO"
               id="duty">
        <id property="idAhcsDutyPay" column="ID_AHCS_DUTY_PAY"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="idAhcsPlanPay" column="ID_AHCS_PLAN_PAY"/>
        <result property="idCopyDuty" column="ID_AHCS_POLICY_DUTY"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
        <result property="baseAmountPay" column="BASE_AMOUNT_PAY"/>
        <result property="settleReason" column="SETTLE_REASON"/>
        <result property="arbitrageFee" column="ARBITRAGE_FEE"/>
        <result property="lawsuitFee" column="LAWSUIT_FEE"/>
        <result property="commonEstimateFee" column="COMMON_ESTIMATE_FEE"/>
        <result property="lawyerFee" column="LAWYER_FEE"/>
        <result property="executeFee" column="EXECUTE_FEE"/>
        <result property="verifyAppraiseFee" column="VERIFY_APPRAISE_FEE"/>
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="isShareAmount" column="IS_SHARE_AMOUNT"/>
        <result property="shareDutyGroup" column="SHARE_DUTY_GROUP"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO" id="simplePrePayMap">
        <result property="policyNo" column="POLICY_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO" id="policyDutyInfo">
        <id property="idAhcsDutyPay" column="ID_AHCS_DUTY_PAY"/>
        <result property="idAhcsPlanPay" column="ID_AHCS_PLAN_PAY"/>
        <result property="idCopyDuty" column="ID_AHCS_DUTY_PAY"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
        <result property="dutyDesc" column="DUTY_DESC"/>
        <result property="baseAmountPay" column="BASE_AMOUNT_PAY"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="isShareAmount" column="IS_SHARE_AMOUNT"/>
        <result property="shareDutyGroup" column="DUTY_SHARED_AMOUNT_MERGE"/>
        <result property="insuranceBeginDate" column="insurance_begin_date"/>
        <result property="insuranceEndDate" column="insurance_end_date"/>
        <!-- 关联责任 明细 -->
        <collection property="dutyDetailPayArr"
                    ofType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO"
                    select="com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper.selectDutyDetail"
                    column="{idAhcsDutyPay = ID_AHCS_DUTY_PAY }">
        </collection>

    </resultMap>

    <!-- 责任信息查询结果映射 -->
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO" id="resultDutyInfo">
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO" id="dutyHistoryPayInfo">
        <result property="policyNo" column="POLICY_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyBaseAmount" column="DUTY_AMOUNT"/>
        <result property="dutyHistoryPay" column="duty_history_pay"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.EpcisPlanDTO" id="epcisPlan">
        <result property="planCode" column="PLAN_CODE"/>
        <collection property="dutyInfo" ofType="com.paic.ncbs.claim.model.dto.settle.EpcisDutyDTO">
            <id property="dutyCode" column="DUTY_CODE"/>
            <result property="dutyHistoryPrepayAmount" column="duty_pay_amount"/>
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO" id="dutyResult">
        <result property="idAhcsDutyPay" column="ID_AHCS_POLICY_DUTY"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO" id="dutyPayMap">
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
    </resultMap>

    <resultMap id="dutyPayNum" type="com.paic.ncbs.claim.model.dto.settle.DutyPayNumDTO">
        <result property="planCode" column="PLAN_CODE" />
        <result property="dutyCode" column="DUTY_CODE" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="dutyPayAmount" column="DUTY_PAY_AMOUNT" />
    </resultMap>

    <resultMap id="dutyPayByCustomResult" type="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        <result property="reportNo" column="REPORT_NO" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="caseNo" column="CASE_NO" />
        <result property="planCode" column="PLAN_CODE" />
        <result property="dutyCode" column="DUTY_CODE" />
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
    </resultMap>

    <resultMap id="dutyPayInfoResult" type="com.paic.ncbs.claim.model.dto.settle.DutyPayInfoDTO">
        <result property="reportNo" column="report_no" />
        <result property="policyNo" column="policy_no" />
        <result property="planCode" column="plan_code" />
        <result property="dutyCode" column="duty_code" />
        <result property="dutyPayAmount" column="duty_pay_amount" />
    </resultMap>

    <sql id="select">
        SELECT
        d.ID_CLM_PLAN_DUTY_PAY ID_AHCS_DUTY_PAY,
        d.PLAN_CODE,
        d.CASE_NO,
        d.DUTY_CODE,
        (select c.id_ahcs_policy_duty from CLMS_policy_info a ,CLMS_policy_plan
        b,CLMS_policy_duty c where a.id_ahcs_policy_info=b.id_ahcs_policy_info
        and b.id_ahcs_policy_plan =c.id_ahcs_policy_plan and
        a.case_no = d.case_no and b.plan_code = d.plan_code and c.duty_code=d.duty_code
        ) id_ahcs_policy_duty,
        (select DI.DUTY_CHINESE_NAME
        from BASE_DUTY_INFO DI
        where DI.duty_code = d.duty_code
        limit 1) DUTY_NAME,
        d.DUTY_PAY_AMOUNT SETTLE_AMOUNT,
        d.DUTY_PAY_LIMIT BASE_AMOUNT_PAY,
        d.SETTLE_REASON,
        d.ARBITRATE_FEE ARBITRAGE_FEE,
        d.LAWSUIT_FEE,
        d.EVALUATION_FEE COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE,
        d.EXECUTE_FEE,
        d.CHECK_FEE VERIFY_FEE,
        d.DECREASE_FEE AWARD_FEE,
        d.CLAIM_TYPE
        FROM clm_plan_duty_pay d
    </sql>

    <!-- 险种责任赔付 批量插入 -->
    <insert id="insertDutyPayInfoList" parameterType="java.util.List">
        INSERT INTO CLM_PLAN_DUTY_PAY (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLM_PLAN_DUTY_PAY,
        DUTY_CODE,
        DUTY_PAY_AMOUNT,
        DUTY_PAY_LIMIT,
        SETTLE_REASON,
        ARBITRATE_FEE,
        LAWSUIT_FEE,
        EVALUATION_FEE,
        LAWYER_FEE,
        EXECUTE_FEE,
        CHECK_FEE,
        CLAIM_TYPE,
        PLAN_CODE,
        CASE_NO,
        CASE_TIMES,
        ID_CLM_BATCH,
        MIGRATE_FROM,
        CURRENCY_CODE,
        ARCHIVE_DATE,
        SUB_TIMES,
        insurance_begin_date,
        insurance_end_date,
        id_ply_risk_property
        )
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idAhcsDutyPay,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.settleAmount,jdbcType=NUMERIC},
            #{item.baseAmountPay,jdbcType=NUMERIC},
            #{item.settleReason,jdbcType=VARCHAR},
            #{item.arbitrageFee,jdbcType=NUMERIC},
            #{item.lawsuitFee,jdbcType=NUMERIC},
            #{item.commonEstimateFee,jdbcType=NUMERIC},
            #{item.lawyerFee,jdbcType=NUMERIC},
            #{item.executeFee,jdbcType=NUMERIC},
            #{item.verifyAppraiseFee,jdbcType=NUMERIC},
            #{item.claimType,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.caseNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.idAhcsBatch,jdbcType=VARCHAR},
            'na',
            '01',
            now(),
            #{item.subTimes,jdbcType=INTEGER},
            #{item.insuranceBeginDate,jdbcType=TIMESTAMP},
             #{item.insuranceEndDate,jdbcType=TIMESTAMP},
            #{item.idPlyRiskProperty,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 根据险种查询责任-->
    <select id="selectByAhcsPlanPayId" parameterType="java.util.Map" resultMap="result">
        select
        d.CREATED_BY,
        d.CREATED_DATE,
        d.UPDATED_BY,
        d.UPDATED_DATE,
        d.ID_CLM_PLAN_DUTY_PAY ID_AHCS_DUTY_PAY,
        (select CLMS_policy_duty.ID_AHCS_POLICY_DUTY from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1)
        ID_AHCS_POLICY_DUTY ,
        d.DUTY_CODE DUTY_CODE,
        (select CLMS_policy_duty.ORG_DUTY_CODE from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) ORG_DUTY_CODE ,
        (select CLMS_policy_duty.DUTY_NAME from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) DUTY_NAME ,
        if((select CLMS_policy_duty.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) = '1',TRUE,FALSE) IS_SHARE_AMOUNT,
        (select CLMS_policy_duty.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) SHARE_DUTY_GROUP,
        d.DUTY_PAY_AMOUNT SETTLE_AMOUNT,
        (select sum(dd.DUTY_PAY_AMOUNT)
         from CLM_PLAN_DUTY_PAY dd
         where dd.PLAN_CODE=d.PLAN_CODE
                and dd.CASE_NO=d.CASE_NO
                and dd.DUTY_CODE=d.DUTY_CODE
            <if test=" caseTimes == 1 ">
                and dd.CASE_TIMES = 1
                and dd.CLAIM_TYPE = '2'
            </if>
            <if test=" caseTimes != 1 ">
                and dd.CASE_TIMES =#{caseTimes,jdbcType=INTEGER}-1
                and dd.CLAIM_TYPE != '2'
            </if>
        ) PAID_AMOUNT,
        d.DUTY_PAY_LIMIT BASE_AMOUNT_PAY,
        d.SETTLE_REASON ,
        d.ARBITRATE_FEE ARBITRAGE_FEE,
        d.LAWSUIT_FEE,
        d.EVALUATION_FEE COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE,
        d.EXECUTE_FEE,
        d.CHECK_FEE VERIFY_FEE,
        d.DECREASE_FEE AWARD_FEE,
        d.CLAIM_TYPE,
        d.SUB_TIMES,
        d.PLAN_CODE,
        d.CASE_NO,
        d.CASE_TIMES,
        d.ID_CLM_BATCH ID_AHCS_BATCH,
        insurance_begin_date,
        insurance_end_date,
        id_ply_risk_property,
        (select sum(ifnull(DUTY_PAY_AMOUNT,0))
        from CLM_PLAN_DUTY_PAY a
        where a.CLAIM_TYPE ='2'
        and case_no = #{caseNo, jdbcType = VARCHAR}
        and plan_code = #{planCode, jdbcType = VARCHAR}
        and a.PLAN_CODE = d.PLAN_CODE
        and a.DUTY_CODE = d.DUTY_CODE group by DUTY_CODE ) preAmount
        from CLM_PLAN_DUTY_PAY d
        where
        d.CASE_NO=#{caseNo, jdbcType = VARCHAR}
        <if test=" idAhcsBatch !=null and idAhcsBatch !='' ">
            and d.ID_CLM_BATCH=#{idAhcsBatch, jdbcType = VARCHAR}
        </if>
        and d.PLAN_CODE=#{planCode, jdbcType = VARCHAR}
        and d.CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        and d.CLAIM_TYPE=#{claimType, jdbcType = VARCHAR}
        order by DUTY_CODE

    </select>

    <select id="getPlanDutyPayByPlan" parameterType="java.util.Map" resultMap="result">
        select
        d.CREATED_BY,
        d.CREATED_DATE,
        d.UPDATED_BY,
        d.UPDATED_DATE,
        d.ID_CLM_PLAN_DUTY_PAY ID_AHCS_DUTY_PAY,
        (select CLMS_policy_duty.ID_AHCS_POLICY_DUTY from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) ID_AHCS_POLICY_DUTY ,
        d.DUTY_CODE DUTY_CODE,
        (select CLMS_policy_duty.ORG_DUTY_CODE from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE  limit 1) ORG_DUTY_CODE ,
        (select CLMS_policy_duty.DUTY_NAME from CLMS_policy_duty where
        ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE  limit 1) DUTY_NAME ,
        if((select CLMS_policy_duty.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty where
            ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) = '1',TRUE,FALSE) IS_SHARE_AMOUNT,
        (select CLMS_policy_duty.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty where
            ID_AHCS_POLICY_PLAN=#{idAhcsPolicyPlan, jdbcType = VARCHAR} and d.DUTY_CODE=clms_policy_duty.DUTY_CODE limit 1) SHARE_DUTY_GROUP,
        d.DUTY_PAY_AMOUNT SETTLE_AMOUNT,
        d.DUTY_PAY_LIMIT BASE_AMOUNT_PAY,
        d.SETTLE_REASON ,
        d.ARBITRATE_FEE ARBITRAGE_FEE,
        d.LAWSUIT_FEE,
        d.EVALUATION_FEE COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE,
        d.EXECUTE_FEE,
        d.CHECK_FEE VERIFY_FEE,
        d.DECREASE_FEE AWARD_FEE,
        d.CLAIM_TYPE,
        d.SUB_TIMES,
        d.PLAN_CODE,
        d.CASE_NO,
        d.CASE_TIMES,
        d.ID_CLM_BATCH ID_AHCS_BATCH
        from CLM_PLAN_DUTY_PAY d
        where
        d.CASE_NO=#{caseNo, jdbcType = VARCHAR}
        and d.PLAN_CODE=#{planCode, jdbcType = VARCHAR}
        and d.CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        and d.CLAIM_TYPE=#{claimType, jdbcType = VARCHAR}
    </select>

    <select id="getDutyByIdPlanPay" parameterType="string" resultMap="dutyResult">
        select pu.ID_AHCS_POLICY_DUTY,
        pu.DUTY_CODE,
        pu.DUTY_NAME,
        pu.ORG_DUTY_CODE
        from CLMS_POLICY_DUTY pu
        where pu.ID_AHCS_POLICY_PLAN = #{idAhcsPlanPay,jdbcType=VARCHAR}
    </select>

    <!-- 根据id查询-->
    <select id="getById" resultMap="result">
        select
        d.ID_CLM_PLAN_DUTY_PAY ID_AHCS_DUTY_PAY,
        d.CREATED_BY ,
        d.CREATED_DATE ,
        d.UPDATED_BY ,
        d.UPDATED_DATE ,
        (select c.id_ahcs_policy_duty from CLMS_policy_info a ,clms_policy_plan
        b,clms_policy_duty c where a.id_ahcs_policy_info=b.id_ahcs_policy_info
        and b.id_ahcs_policy_plan =c.id_ahcs_policy_plan and
        a.case_no = d.case_no and b.plan_code = d.plan_code and c.duty_code=d.duty_code
        ) id_ahcs_policy_duty,
        (select p.id_clm_plan_pay from clm_plan_pay p
        where p.case_no = d.case_no
        and p.case_times = d.case_times
        and p.claim_type = #{claimType}
        and p.plan_code = d.plan_code
        <if test="claimType == '2' ">
            and p.sub_times = d.sub_times
        </if>
        ) ID_AHCS_PLAN_PAY,
        d.DUTY_CODE DUTY_CODE,
        (select pd.duty_name from CLMS_policy_duty pd where pd.duty_code=d.duty_code limit 1) DUTY_NAME,
        if((select pd.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty pd where pd.DUTY_CODE=d.duty_code limit 1) = '1',TRUE,FALSE) IS_SHARE_AMOUNT,
        (select pd.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty pd where pd.DUTY_CODE=d.duty_code limit 1) SHARE_DUTY_GROUP,
        d.DUTY_PAY_AMOUNT SETTLE_AMOUNT,
        d.DUTY_PAY_LIMIT BASE_AMOUNT_PAY,
        d.SETTLE_REASON SETTLE_REASON,
        d.ARBITRATE_FEE ARBITRAGE_FEE,
        d.LAWSUIT_FEE LAWSUIT_FEE,
        d.EVALUATION_FEE COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE LAWYER_FEE,
        d.EXECUTE_FEE EXECUTE_FEE,
        d.CHECK_FEE VERIFY_FEE,
        d.DECREASE_FEE AWARD_FEE,
        d.CLAIM_TYPE CLAIM_TYPE,
        d.SUB_TIMES SUB_TIMES,
        (select b.policy_no from clm_case_base b where b.case_no=d.case_no and b.case_times=d.case_times) POLICY_NO,
        d.PLAN_CODE PLAN_CODE,
        d.ID_CLM_BATCH ID_AHCS_BATCH,
        d.CASE_TIMES CASE_TIMES,
        d.CASE_NO CASE_NO
        from clm_plan_duty_pay d
        where d.ID_CLM_PLAN_DUTY_PAY = #{idAhcsDutyPay, jdbcType = VARCHAR}
    </select>

    <!--  理算-根据险种查询责任（抄单）-->
    <select id="selectPolicyDuty" resultMap="policyDutyInfo">
        select CREATED_BY,
               UPDATED_BY,
               ID_AHCS_POLICY_DUTY                          ID_AHCS_DUTY_PAY,
               ID_AHCS_POLICY_PLAN                          ID_AHCS_PLAN_PAY,
               DUTY_CODE,
               ORG_DUTY_CODE,
               DUTY_NAME,
               DUTY_DESC,
               DUTY_AMOUNT                                  BASE_AMOUNT_PAY,
               IF(IS_DUTY_SHARED_AMOUNT = '1', TRUE, FALSE) IS_SHARE_AMOUNT,
               DUTY_SHARED_AMOUNT_MERGE,
               insurance_begin_date,insurance_end_date
        from CLMS_POLICY_DUTY
        where ID_AHCS_POLICY_PLAN = #{idAhcsPlanPay}
    </select>

    <!-- 批量更新 -->
    <update id="updateDutyPayInfoList" parameterType="java.util.List">
         update clm_plan_duty_pay
            set
            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="dutyCode != null and dutyCode != '' ">
                DUTY_CODE = #{dutyCode},
            </if>
            DUTY_PAY_AMOUNT = #{settleAmount,jdbcType=NUMERIC},

            <if test="chgDutyPayAmount != null ">
                CHG_DUTY_PAY_AMOUNT = #{chgDutyPayAmount,jdbcType=NUMERIC},
            </if>
            <if test="baseAmountPay != null ">
                DUTY_PAY_LIMIT = #{baseAmountPay},
            </if>
            SETTLE_REASON = #{settleReason, jdbcType = VARCHAR},

            ARBITRATE_FEE = #{arbitrageFee,jdbcType=NUMERIC},

            LAWSUIT_FEE = #{lawsuitFee,jdbcType=NUMERIC},

            EVALUATION_FEE = #{commonEstimateFee,jdbcType=NUMERIC},

            LAWYER_FEE = #{lawyerFee,jdbcType=NUMERIC},

            EXECUTE_FEE = #{executeFee,jdbcType=NUMERIC},

            CHECK_FEE = #{verifyAppraiseFee,jdbcType=NUMERIC},

            <if test="claimType != null ">
                CLAIM_TYPE = #{claimType},
            </if>

            <if test="planCode != null ">
                PLAN_CODE = #{planCode},
            </if>
            <if test="idAhcsBatch != null ">
                ID_CLM_BATCH = #{idAhcsBatch},
            </if>
            <if test="caseTimes != null ">
                CASE_TIMES = #{caseTimes},
            </if>
            <if test="caseNo != null ">
                CASE_NO = #{caseNo},
            </if>
            UPDATED_DATE = SYSDATE()
            WHERE ID_CLM_PLAN_DUTY_PAY=#{idAhcsDutyPay}
    </update>

    <!-- 批量更新，自动理算提交 -->
    <update id="updateDutyPayInfoListByAuto" parameterType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        update clm_plan_duty_pay
        set
        <if test="updatedBy != null and updatedBy != '' ">
            UPDATED_BY = #{updatedBy},
        </if>
        <if test="dutyCode != null and dutyCode != '' ">
            DUTY_CODE = #{dutyCode},
        </if>
        DUTY_PAY_AMOUNT = #{settleAmount,jdbcType=NUMERIC},

        <if test="chgDutyPayAmount != null ">
            CHG_DUTY_PAY_AMOUNT = #{chgDutyPayAmount,jdbcType=NUMERIC},
        </if>
        duty_pay_amount_auto = #{settleAmount,jdbcType=NUMERIC},

        <if test="baseAmountPay != null ">
            DUTY_PAY_LIMIT = #{baseAmountPay},
        </if>
        SETTLE_REASON = #{settleReason, jdbcType = VARCHAR},

        settle_reason_auto = #{settleReason, jdbcType = VARCHAR},

        ARBITRATE_FEE = #{arbitrageFee,jdbcType=NUMERIC},

        LAWSUIT_FEE = #{lawsuitFee,jdbcType=NUMERIC},

        EVALUATION_FEE = #{commonEstimateFee,jdbcType=NUMERIC},

        LAWYER_FEE = #{lawyerFee,jdbcType=NUMERIC},

        EXECUTE_FEE = #{executeFee,jdbcType=NUMERIC},

        CHECK_FEE = #{verifyAppraiseFee,jdbcType=NUMERIC},

        <if test="claimType != null ">
            CLAIM_TYPE = #{claimType},
        </if>

        <if test="planCode != null ">
            PLAN_CODE = #{planCode},
        </if>
        <if test="idAhcsBatch != null ">
            ID_CLM_BATCH = #{idAhcsBatch},
        </if>
        <if test="caseTimes != null ">
            CASE_TIMES = #{caseTimes},
        </if>
        <if test="caseNo != null ">
            CASE_NO = #{caseNo},
        </if>
        UPDATED_DATE = SYSDATE()
        WHERE ID_CLM_PLAN_DUTY_PAY=#{idAhcsDutyPay}
    </update>

    <update id="updateDutyPayInfoListForTPAReSettle" parameterType="java.util.List">

        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            update clm_plan_duty_pay
            set
            <if test="item.updatedBy != null and item.updatedBy != '' ">
                UPDATED_BY = #{item.updatedBy},
            </if>
            <if test="item.dutyCode != null and item.dutyCode != '' ">
                DUTY_CODE = #{item.dutyCode},
            </if>
            DUTY_PAY_AMOUNT = #{item.settleAmount,jdbcType=NUMERIC},
            <if test="item.baseAmountPay != null ">
                DUTY_PAY_LIMIT = #{item.baseAmountPay},
            </if>
            SETTLE_REASON = #{item.settleReason, jdbcType = VARCHAR},

            ARBITRATE_FEE = #{item.arbitrageFee,jdbcType=NUMERIC},

            LAWSUIT_FEE = #{item.lawsuitFee,jdbcType=NUMERIC},

            EVALUATION_FEE = #{item.commonEstimateFee,jdbcType=NUMERIC},

            LAWYER_FEE = #{item.lawyerFee,jdbcType=NUMERIC},

            EXECUTE_FEE = #{item.executeFee,jdbcType=NUMERIC},

            CHECK_FEE = #{item.verifyAppraiseFee,jdbcType=NUMERIC},

            <if test="item.claimType != null ">
                CLAIM_TYPE = #{item.claimType},
            </if>

            <if test="item.planCode != null ">
                PLAN_CODE = #{item.planCode},
            </if>
            <if test="item.idAhcsBatch != null ">
                ID_CLM_BATCH = #{item.idAhcsBatch},
            </if>
            <if test="item.caseTimes != null ">
                CASE_TIMES = #{item.caseTimes},
            </if>
            <if test="item.caseNo != null ">
                CASE_NO = #{item.caseNo},
            </if>
            UPDATED_DATE = sysdate()
            WHERE CASE_NO = #{item.caseNo} and CASE_TIMES = #{item.caseTimes} and CLAIM_TYPE = #{item.claimType}  and PLAN_CODE = #{item.planCode} and
            DUTY_CODE = #{item.dutyCode}
        </foreach>
    </update>

    <!-- 更新赔付记录的反洗钱理算依据 -->
    <update id="updateFxqSettleReason">
        UPDATE CLM_PLAN_DUTY_PAY A
        SET A.SETTLE_REASON = #{settleReason, jdbcType = VARCHAR}, A.UPDATED_DATE = sysdate()
        WHERE EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO B
        WHERE A.CASE_NO = B.CASE_NO
        AND B.REPORT_NO = #{reportNo, jdbcType=VARCHAR})
        AND A.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        AND A.CLAIM_TYPE = '1'
        AND A.SETTLE_REASON IS NOT NULL
    </update>

    <select id="getDutyHistoryPay" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultType="java.math.BigDecimal">
        SELECT
        ifnull(SUM(ifnull(A.DUTY_PAY_AMOUNT,0)),0)
        FROM CLM_PLAN_DUTY_PAY A,
        CLM_CASE_BASE C,
        CLMS_POLICY_CLAIM_CASE B
        WHERE A.CASE_TIMES=C.CASE_TIMES
        AND A.CASE_NO=C.CASE_NO
        AND A.CASE_NO = B.CASE_NO
        AND A.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND A.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND C.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        <if test="subpolicyNo != null ">
            AND B.SUBPOLICY_NO = #{subpolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="insuredCode != null ">
            AND B.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        </if>
        AND (C.CASE_STATUS = '0' or C.CASE_STATUS = '5')
        AND A.CLAIM_TYPE = '1'
    </select>

    <select id="getDutyBaseAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultType="java.math.BigDecimal">
        SELECT PD.DUTY_AMOUNT
        FROM CLMS_POLICY_DUTY PD,
        CLMS_POLICY_PLAN PP,
        CLMS_POLICY_INFO PI
        WHERE PI.ID_AHCS_POLICY_INFO = PP.ID_AHCS_POLICY_INFO
        AND PD.ID_AHCS_POLICY_PLAN = PP.ID_AHCS_POLICY_PLAN
        AND PD.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND PP.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND PI.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        AND PI.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getDutyPayInfoByReportNoList" resultMap="result">
        <include refid="select"/>
        ,CLM_POLICY_PAY p
        WHERE d.CASE_NO = p.CASE_NO
        and d.CASE_TIMES = p.CASE_TIMES
        and p.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and p.CASE_TIMES= #{caseTimes,jdbcType=INTEGER}
    </select>

    <select id="getDutyPayInfoList" resultMap="result">
        <include refid="select"/>
        WHERE d.CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        AND d.CLAIM_TYPE=#{claimType,jdbcType=VARCHAR}
        AND d.CASE_NO=#{caseNo,jdbcType=VARCHAR}
        <if test="idAhcsBatch != null ">
            and d.ID_CLM_BATCH=#{idAhcsBatch,jdbcType=VARCHAR}
        </if>

    </select>

    <delete id="deleteByBatchId" parameterType="java.lang.String">
        delete from CLM_PLAN_DUTY_PAY where ID_CLM_BATCH = #{idAhcsBatch,jdbcType=VARCHAR} and
        CLAIM_TYPE=#{claimType,jdbcType=VARCHAR}
    </delete>

    <select id="getPreDutyPays" resultMap="duty">
        SELECT
        d.ID_CLM_PLAN_DUTY_PAY ID_AHCS_DUTY_PAY,
        p.policy_no,
        d.PLAN_CODE,
        d.CASE_NO,
        d.DUTY_CODE,
        (select DI.DUTY_CHINESE_NAME
        from BASE_DUTY_INFO DI
        where DI.duty_code = d.duty_code
        limit 1) DUTY_NAME,
        (select pd.ID_AHCS_POLICY_DUTY from CLMS_policy_duty pd, CLMS_policy_plan pp, CLMS_policy_info pi
        where pd.id_ahcs_policy_plan = pp.id_ahcs_policy_plan and pi.id_ahcs_policy_info = pp.id_ahcs_policy_info and
        pi.case_no =p.CASE_NO limit 1) ID_AHCS_POLICY_DUTY,
        d.DUTY_PAY_AMOUNT SETTLE_AMOUNT,
        d.DUTY_PAY_LIMIT BASE_AMOUNT_PAY,
        d.SETTLE_REASON,
        d.ARBITRATE_FEE ARBITRAGE_FEE,
        d.LAWSUIT_FEE,
        d.EVALUATION_FEE COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE,
        d.EXECUTE_FEE,
        d.CHECK_FEE VERIFY_FEE,
        d.DECREASE_FEE AWARD_FEE,
        d.CLAIM_TYPE
        FROM CLM_PLAN_DUTY_PAY d,CLM_POLICY_PAY p
        WHERE d.CASE_NO = p.CASE_NO
        and d.CASE_TIMES = p.CASE_TIMES
        and p.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and p.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        and d.CLAIM_TYPE = #{claimType,jdbcType=VARCHAR}
        and exists (select 1
        from CLMS_policy_info b
        where p.report_no = b.report_no
        and p.case_no = b.case_no)
    </select>

    <select id="getNoEndCasePreDutyAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultType="java.math.BigDecimal">
        SELECT sum(dp.duty_pay_amount) SETTLE_AMOUNT
        FROM clm_plan_duty_pay dp,
        clm_policy_pay pp,
        CLMS_policy_claim_case pc,
        CLMS_case_process cp
        where pc.report_no = pp.report_no
        and pc.policy_no = pp.policy_no
        and cp.report_no = pp.report_no
        and dp.case_no = pp.case_no
        and dp.plan_code = #{planCode,jdbcType=VARCHAR}
        and dp.duty_code = #{dutyCode,jdbcType=VARCHAR}
        and dp.claim_type = '2'
        and pp.policy_no = #{policyNo,jdbcType=VARCHAR}
        and pp.report_no != #{reportNo,jdbcType=VARCHAR}
        and dp.case_times = cp.case_times
        <if test="caseNo != null ">
            and pp.case_no = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="subpolicyNo != null ">
            and pc.subpolicy_no = #{subpolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="insuredCode != null ">
            and pc.insured_Code = #{insuredCode,jdbcType=VARCHAR}
        </if>
        and cp.process_status in ('04','03','02')
    </select>

    <select id="getNoEndCasePreDutyPays" parameterType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO"
            resultMap="epcisPlan">
        SELECT ifnull(pp.org_plan_code,dp.plan_code) plan_code,
        ifnull(pd.org_duty_code,dp.duty_code) duty_code,
        sum(ifnull(dp.duty_pay_amount,0)) duty_pay_amount
        FROM
        CLMS_policy_info pi,
        CLMS_policy_plan pp,
        CLMS_policy_duty pd,
        clm_plan_duty_pay dp
        where
        pi.id_ahcs_policy_info=pp.id_ahcs_policy_info
        and pp.id_ahcs_policy_plan=pd.id_ahcs_policy_plan
        and dp.claim_type = '2'
        and pp.plan_code=dp.plan_code
        and pd.duty_code=dp.duty_code
        and pi.case_no = dp.case_no
        and pi.policy_no = #{policyNo, jdbcType=VARCHAR}
        and exists (select 1 from CLMS_policy_claim_case pc where pc.insured_Code = #{insuredCode, jdbcType=VARCHAR} and
        pi.report_no = pc.report_no
        and pi.case_no = pc.case_no and pi.policy_no=pc.policy_no)
        and exists (select 1 from CLMS_case_process cp where cp.process_status in ('04','03','02') and pi.report_no =
        cp.report_no and dp.case_times =cp.case_times)
        group by ifnull(pp.org_plan_code,dp.plan_code), ifnull(pd.org_duty_code,dp.duty_code)

    </select>

    <!--本保单同一责任的未结案案件的历次已审核通过的预赔付赔款总金额不能超过最大给付额，
    超过最大给付额时不允许发送，弹出提示：前期已预赔付**元，总金额已超过**责任最大给付额，-->
    <select id="getPrePay" resultMap="simplePrePayMap">
        SELECT dp.case_no, dp.plan_code, dp.duty_code, sum(ifnull(dp.duty_pay_amount, 0)) settle_amount,
        (select p.policy_cer_no from CLMS_policy_info p where p.case_no=dp.case_no limit 1) policy_cer_no,
        (select p.policy_no from CLMS_policy_claim_case p where p.case_no=dp.case_no limit 1) policy_no
        FROM clm_plan_duty_pay dp,
        clm_policy_pay pp,
        CLMS_policy_claim_case pc
        where pp.case_no = pc.case_no
        and pp.case_no = dp.case_no
        and pc.policy_no in (select policy_no from CLMS_policy_claim_case where report_no =
        #{reportNo, jdbcType=VARCHAR} )
        and pc.insured_code in (select insured_code from CLMS_policy_claim_case where report_no =
        #{reportNo, jdbcType=VARCHAR})
        and dp.claim_type = '2'
        and exists (select 1 from clm_case_base cp where cp.case_no =dp.case_no and cp.case_status = '1' )
        group by dp.case_no, dp.plan_code, dp.duty_code

    </select>

    <select id="getDutyHistoryPayInfoPolicyCopy" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultMap="dutyHistoryPayInfo">
        select sum(duty_history_pay) duty_history_pay,duty_code  from
            (SELECT
                 PI.POLICY_NO,
                 PP.PLAN_CODE,
                 PD.DUTY_CODE,
                 PD.DUTY_AMOUNT,pi.CASE_NO,
                 ((SELECT
                       ifnull(SUM(ifnull(A.DUTY_PAY_AMOUNT, 0)), 0) FROM CLM_PLAN_DUTY_PAY A,clms_POLICY_CLAIM_CASE B,clms_CASE_PROCESS
                     C,clms_policy_info d,clms_policy_plan e
                                                                       ,clms_policy_duty f
                   where
                       d.id_ahcs_policy_info=e.id_ahcs_policy_info
                     and e.id_ahcs_policy_plan=f.id_ahcs_policy_plan
                     and a.case_no=d.case_no
                     and e.plan_code=a.plan_code
                     and f.duty_code=a.duty_code
                     and B.POLICY_NO = #{policyNo}
                     AND (A.DUTY_CODE = PD.Duty_Code or A.DUTY_CODE = PD.ORG_Duty_Code or f.org_duty_code=PD.Duty_Code )
                     AND (A.PLAN_CODE = PP.PLAN_CODE or A.PLAN_CODE = PP.ORG_PLAN_CODE or e.org_plan_code= PP.PLAN_CODE)
                     AND A.CLAIM_TYPE = '1'
                     AND A.CASE_NO = B.CASE_NO
                     AND B.REPORT_NO= C.REPORT_NO
                     AND A.CASE_TIMES = C.CASE_TIMES
                     AND (C.PROCESS_STATUS = '05' or C.PROCESS_STATUS = '06'))
                     ) +
                 ((SELECT
                       ifnull(SUM(ifnull(A.DUTY_PAY_AMOUNT, 0)), 0) FROM CLM_PLAN_DUTY_PAY A,clms_POLICY_CLAIM_CASE B,clms_CASE_PROCESS
                     C,clms_policy_info d,clms_policy_plan e
                                                                       ,clms_policy_duty f
                   where
                       d.id_ahcs_policy_info=e.id_ahcs_policy_info
                     and e.id_ahcs_policy_plan=f.id_ahcs_policy_plan
                     and a.case_no=d.case_no
                     and e.plan_code=a.plan_code
                     and f.duty_code=a.duty_code
                     and B.POLICY_NO = #{policyNo}
                     AND (A.DUTY_CODE = PD.Duty_Code or A.DUTY_CODE = PD.ORG_Duty_Code or f.org_duty_code=PD.Duty_Code )
                     AND (A.PLAN_CODE = PP.PLAN_CODE or A.PLAN_CODE = PP.ORG_PLAN_CODE or e.org_plan_code= PP.PLAN_CODE)
                     AND A.CLAIM_TYPE = '2'
                     AND A.CASE_NO = B.CASE_NO
                     AND B.REPORT_NO= C.REPORT_NO
                     AND A.CASE_TIMES = C.CASE_TIMES
                     )
                 ) duty_history_pay
             FROM CLMS_POLICY_DUTY PD,
                  CLMS_POLICY_PLAN PP, CLMS_POLICY_INFO PI
             WHERE PI.ID_AHCS_POLICY_INFO =
                   PP.ID_AHCS_POLICY_INFO
               AND PD.ID_AHCS_POLICY_PLAN =
                   PP.ID_AHCS_POLICY_PLAN
               AND PI.POLICY_NO = #{policyNo} ) w group by duty_code
    </select>

    <!-- 根据险种查询赔款金额大于0的责任代码-->
    <select id="getDutyPayInfoByPlan" resultType="string">
        select d.DUTY_CODE
        from CLM_PLAN_DUTY_PAY d
        where d.CASE_NO=#{caseNo}
        and d.CASE_TIMES=#{caseTimes}
        and d.PLAN_CODE=#{planCode}
        and d.CLAIM_TYPE=#{claimType}
        and d.DUTY_PAY_AMOUNT is not null
        and d.DUTY_PAY_AMOUNT > 0
    </select>

    <!-- 根据险种查询赔款的责任代码-->
    <select id="getDutyPayInfoByPlanCode" resultType="string">
        select d.DUTY_CODE
        from CLM_PLAN_DUTY_PAY d
        where d.CASE_NO=#{caseNo}
        and d.CASE_TIMES=#{caseTimes}
        and d.PLAN_CODE=#{planCode}
        limit 1
    </select>

    <!--根据险种id查询最大保额的责任-->
    <select id="getDutyCodeByIdPlan" resultType="string">
        select pu.DUTY_CODE
        from CLMS_POLICY_DUTY pu
        where pu.ID_AHCS_POLICY_PLAN = #{idAhcsPlanPay,jdbcType=VARCHAR}
        and pu.DUTY_AMOUNT = (
        select ifnull(max(pu.DUTY_AMOUNT),0)
        from CLMS_POLICY_DUTY pu
        where pu.ID_AHCS_POLICY_PLAN=#{idAhcsPlanPay,jdbcType=VARCHAR}
        )
    </select>

    <!-- 获取预赔总金额 -->
    <select id="getDutyPrePayAomunt" resultMap="duty">
        SELECT A.POLICY_NO,T.DUTY_CODE, SUM(T.DUTY_PAY_AMOUNT) SETTLE_AMOUNT
        FROM CLM_PLAN_DUTY_PAY T, CLMS_POLICY_INFO A
        WHERE
        A.REPORT_NO = #{reportNo}
        AND A.CASE_NO = T.CASE_NO
        AND T.CASE_TIMES = #{caseTimes}
        AND T.CLAIM_TYPE = '2'
        GROUP BY A.POLICY_NO,T.DUTY_CODE
    </select>

    <!-- 根据报案号和赔付次数查询理算金额大于0的责任  -->
    <select id="getDutyPayInfos" resultMap="dutyPayMap">
        select t.case_no,
        t1.duty_code,
        t1.plan_code,
        t1.duty_pay_amount
        from clm_policy_pay t, clm_plan_duty_pay t1
        where t.case_no = t1.case_no
        and t.case_times = t1.case_times
        and t1.duty_pay_amount > 0
        and t.report_no = #{reportNo, jdbcType=VARCHAR}
        and t.case_times = #{caseTimes, jdbcType=INTEGER}
        and t.policy_pay > 0
        and t1.claim_type='1'
    </select>

    <select id="getPrePayingAmount" parameterType="com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO" resultType="java.math.BigDecimal">
        select ifnull(sum(d.duty_pay_amount), 0)  from CLMS_duty_prepay_info d ,clms_prepay_info p where d.id_ahcs_prepay_info = p.id_ahcs_prepay_info
        and  d.policy_no = #{policyNo, jdbcType=VARCHAR} and d.plan_code = #{planCode, jdbcType=VARCHAR} and d.duty_code =#{dutyCode, jdbcType=VARCHAR}
        and d.report_no = #{reportNo, jdbcType=VARCHAR} and p.status ='1'
    </select>

    <select id="getPolicyDutyPayedAmount" resultType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        select concat(c.PLAN_CODE,c.DUTY_CODE) dutyCode ,
            sum(c.DUTY_PAY_AMOUNT) settleAmount
        from clm_policy_pay a,clm_plan_duty_pay c
        where a.CASE_NO =c.CASE_NO
        and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        and c.CLAIM_TYPE = '1'
        and exists (select 1 from clm_whole_case_base d where d.REPORT_NO=a.REPORT_NO and d.WHOLE_CASE_STATUS='0' )
        group by c.PLAN_CODE,c.DUTY_CODE
    </select>

    <select id="getPolicyDutyPrePayedAmount" resultType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        select concat(c.PLAN_CODE,c.DUTY_CODE) dutyCode ,
            sum(c.DUTY_PAY_AMOUNT) settleAmount
        from clm_policy_pay a,clm_plan_duty_pay c
        where a.CASE_NO = c.CASE_NO
        and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        and c.CLAIM_TYPE = '2'
        group by c.PLAN_CODE,c.DUTY_CODE
    </select>

    <select id="getPolicyDutyPayedAmountByReportNo" resultType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        select concat(POLICY_NO, concat(c.PLAN_CODE,c.DUTY_CODE)) dutyCode ,
        sum(c.DUTY_PAY_AMOUNT) settleAmount
        from clms_policy_info a,clms_POLICY_CLAIM_CASE B,clm_plan_duty_pay c
        where a.CASE_NO = c.CASE_NO
        and a.CASE_NO = b.CASE_NO
        and B.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        and a.POLICY_NO in(select b.POLICY_NO from clms_policy_info b where b.REPORT_NO=#{reportNo, jdbcType=VARCHAR})
        and (c.CLAIM_TYPE = '2' or ( exists (select 1 from clm_whole_case_base d where d.REPORT_NO=a.REPORT_NO and d.WHOLE_CASE_STATUS='0' )
        and c.CLAIM_TYPE = '1')
        )
        group by a.POLICY_NO ,c.PLAN_CODE,c.DUTY_CODE
    </select>

    <select id="getDutyHistoryAmount" resultType="java.math.BigDecimal" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam">
        select ifnull(sum(a.DUTY_PAY_AMOUNT),0) from clm_plan_duty_pay a,clms_policy_claim_case b
        where a.CASE_NO = b.CASE_NO
        and b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        and a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        and (a.CLAIM_TYPE ='2' or (a.CLAIM_TYPE='1' and exists(select 1 from clm_whole_case_base c where c.REPORT_NO=b.REPORT_NO and c.WHOLE_CASE_STATUS='0' and c.INDEMNITY_CONCLUSION='1')))
        <if test="dutyCode != null ">
            and a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        </if>
        <if test="insuredCode != null ">
            and b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        </if>
        <if test="dutyCodeList != null ">
            and a.DUTY_CODE in
            <foreach collection="dutyCodeList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getEstimateAmount" resultType="java.math.BigDecimal">
        select
        (case when
        (select sum(d.CHANGE_AMOUNT) from clms_estimate_change d where d.report_no=#{reportNo,jdbcType=VARCHAR} and d.case_times = #{caseTimes, jdbcType=INTEGER} and d.IS_EFFECTIVE='Y')>0
        then (select sum(d.CHANGE_AMOUNT) from clms_estimate_change d where d.report_no=#{reportNo,jdbcType=VARCHAR} and d.case_times = #{caseTimes, jdbcType=INTEGER} and d.IS_EFFECTIVE='Y')
        else (select c.ESTIMATE_AMOUNT from clms_estimate_record c where c.report_no=#{reportNo,jdbcType=VARCHAR} and c.case_times = #{caseTimes, jdbcType=INTEGER} order by c.EFFECTIVE_TIME desc limit 1) end) as estimateAmount
    </select>

    <select id="getDutyPay" resultMap="duty">
        SELECT CASE_NO, CASE_TIMES, PLAN_CODE, DUTY_CODE, CLAIM_TYPE, DUTY_PAY_AMOUNT SETTLE_AMOUNT
        FROM CLM_PLAN_DUTY_PAY
        WHERE CASE_NO = #{caseNo}
        AND CASE_TIMES = #{caseTimes}
        AND PLAN_CODE = #{planCode}
        AND DUTY_CODE = #{dutyCode}
        AND CLAIM_TYPE = '1'
    </select>

    <select id="getDutyPayNum" resultMap="dutyPayNum">
        select pdp.PLAN_CODE,pdp.DUTY_CODE,pdp.CASE_TIMES,pdp.DUTY_PAY_AMOUNT
        from clm_plan_duty_pay pdp join clm_case_base cb on pdp.case_no = cb.case_no and pdp.case_times = cb.case_times
        and cb.CASE_STATUS = '0' and cb.INDEMNITY_CONCLUSION = '1' and cb.REPORT_NO != #{reportNo,jdbcType=VARCHAR}
        and cb.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        where pdp.duty_pay_amount > 0 and pdp.case_times = (
        SELECT MAX(t2.case_times)
        FROM clm_plan_duty_pay t2
        WHERE t2.CASE_NO = pdp.CASE_NO
        )
    </select>

    <select id="listDutyPayByCustom" resultMap="dutyPayByCustomResult" parameterType="com.paic.ncbs.claim.model.dto.openapi.DutyPayByCustomReqDTO">
        select * from
        (select
        a.REPORT_NO,
        a.POLICY_NO,
        a.CASE_NO,
        d.PLAN_CODE,
        d.DUTY_CODE,
        d.CLAIM_TYPE,
        d.DUTY_PAY_AMOUNT SETTLE_AMOUNT
        from clm_case_base a
        inner join CLMS_REPORT_CUSTOMER b on b.REPORT_NO = a.REPORT_NO
        inner join clm_plan_duty_pay d on d.CASE_NO = a.CASE_NO and d.DUTY_PAY_AMOUNT is not null and d.DUTY_PAY_AMOUNT &lt;&gt; 0
        inner join CLM_WHOLE_CASE_BASE e on e.REPORT_NO = a.REPORT_NO and e.WHOLE_CASE_STATUS = '0' and e.INDEMNITY_CONCLUSION = '1'
        where 1=1
        <if test="clientNo != null ">
            and b.CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateNo != null ">
            and b.CERTIFICATE_NO = #{certificateNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateType != null ">
            and b.CERTIFICATE_TYPE = #{certificateType, jdbcType=VARCHAR}
        </if>
        <if test="name != null ">
            and b.NAME = #{name, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and d.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and d.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        limit 0,10) tmp1
        union all
        select * from
        (select
        a.REPORT_NO,
        a.POLICY_NO,
        a.CASE_NO,
        d.PLAN_CODE,
        d.DUTY_CODE,
        '' as CLAIM_TYPE,
        d.ESTIMATE_AMOUNT SETTLE_AMOUNT
        from clm_case_base a
        inner join CLMS_REPORT_CUSTOMER b on b.REPORT_NO = a.REPORT_NO
        inner join clms_estimate_duty_record d on d.CASE_NO = a.CASE_NO and d.estimate_type = '02' and d.IS_EFFECTIVE = 'Y' and d.ESTIMATE_AMOUNT > 0
        inner join CLM_WHOLE_CASE_BASE e on e.REPORT_NO = a.REPORT_NO and e.WHOLE_CASE_STATUS &lt;&gt; '0' and (e.INDEMNITY_CONCLUSION = '1' or e.INDEMNITY_CONCLUSION is null)
        where 1=1
        <if test="clientNo != null ">
            and b.CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateNo != null ">
            and b.CERTIFICATE_NO = #{certificateNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateType != null ">
            and b.CERTIFICATE_TYPE = #{certificateType, jdbcType=VARCHAR}
        </if>
        <if test="name != null ">
            and b.NAME = #{name, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and d.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and d.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        limit 0,10) tmp2
    </select>

    <!-- 根据报案号和赔付次数查询理算金额大于0的责任  -->
    <select id="getDutyPayInfoByReportAndCaseTimes" resultMap="dutyPayInfoResult">
        select
            distinct
            t.report_no,
            t.policy_no,
            t1.duty_code,
            t1.plan_code,
            t1.duty_pay_amount
        from
              clm_policy_pay t, clm_plan_duty_pay t1
        where t.case_no = t1.case_no
          and t1.duty_pay_amount > 0
          and t.report_no = #{reportNo, jdbcType=VARCHAR}
    </select>

    <select id="getDutyPayInfoBy" resultMap="dutyPayInfoResult">
        select distinct t.report_no,t.policy_no,t1.duty_code,t1.plan_code,t1.duty_pay_amount,t.policy_no,t1.duty_code from clm_policy_pay t, clm_plan_duty_pay t1
        where t.case_no = t1.case_no and t1.duty_pay_amount > 0
        and t.report_no = #{reportNo, jdbcType=VARCHAR}
    </select>
    <select id="getDutyPayAmount" resultType="com.paic.ncbs.claim.model.dto.settle.DutyPayInfoDTO">
        select b.policy_no policyNo,a.plan_code planCode,a.duty_code dutyCode,a.DUTY_PAY_AMOUNT dutyPayAmount
        from clm_plan_duty_pay a ,clms_policy_claim_case b
        where a.case_no=b.case_no
        and b.report_no=#{reportNo}
        and a.CASE_TIMES=#{caseTimes}
        and a.DUTY_PAY_AMOUNT>0
    </select>

    <select id="getdutyShareInfo" resultType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        select   a.POLICY_NO policyNo, c.IS_DUTY_SHARED_AMOUNT  isShareAmount,
        c.DUTY_SHARED_AMOUNT_MERGE shareDutyGroup,
        c.DUTY_AMOUNT baseAmountPay
        from clms_policy_info a,clms_policy_plan b,clms_policy_duty c
        where a.ID_AHCS_POLICY_INFO=b.ID_AHCS_POLICY_INFO
        and b.ID_AHCS_POLICY_PLAN=c.ID_AHCS_POLICY_PLAN
        and b.plan_code=#{planCode}
        and c.duty_code=#{dutyCode}
        and REPORT_NO=#{reportNo}
    </select>

    <select id="getDutyName" resultType="com.paic.ncbs.claim.model.dto.duty.DutyPayDTO">
        select c.duty_name
        from clms_policy_info a,clms_policy_plan b,clms_policy_duty c
        where a.ID_AHCS_POLICY_INFO=b.ID_AHCS_POLICY_INFO
          and b.ID_AHCS_POLICY_PLAN=c.ID_AHCS_POLICY_PLAN
          and b.plan_code=#{planCode}
          and c.duty_code=#{dutyCode}
          and REPORT_NO=#{reportNo}
        limit 1
    </select>
</mapper>