<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper">

	<select id="getInsuredList" parameterType="com.paic.ncbs.claim.model.dto.ocas.OcasDTO"
			resultType="com.paic.ncbs.claim.model.dto.ocas.OcasDTO">
		select a.policy_no         policyNo,
			a.certificate_no       certificateNo,
			a.certificate_type     certificateType,
			a.name                 insuredName,
			a.birthday             birthday,
			a.age                  age,
			a.sex_code             sexCode,
			a.personnel_attribute  personnelAttribute,
		    a.id_ply_risk_person   clientNo,
			b.department_code      departmentCode,
			b.insurance_begin_date insuranceBeginDate,
			b.insurance_end_date   insuranceEndDate,
			b.status               status,
			c.name                 applicantName,
			a.mobile_telephone     mobileTelephone,
		    a.PROFESSION_CLASS     professionClass,
		    a.CLIENT_NO clientNo
		from ply_risk_person a, ply_base_info b ,ply_applicant_info c
		where a.policy_no = b.policy_no
		and b.policy_no = c.policy_no
		<if test="certificateNo != null and certificateNo != '' " >
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_no = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="insuredName != null and insuredName != ''  ">
			and a.name = #{insuredName,jdbcType=VARCHAR}
		</if>
		<if test="applicantName != null and applicantName != ''  ">
			and c.name = #{applicantName,jdbcType=VARCHAR}
		</if>
		<if test="applicantCertificateNo != null and applicantCertificateNo != ''  ">
			and c.certificate_no = #{applicantCertificateNo,jdbcType=VARCHAR}
		</if>
		order by b.insurance_end_date desc
	</select>


    <!--	方案号-->
	<select id="getRiskGroupNo" parameterType="com.paic.ncbs.claim.model.dto.ocas.OcasDTO"
			resultType="java.lang.String">
		select `risk_Group_No` from ply_risk_group where `ID_PLY_RISK_GROUP`=
		 (select distinct  ID_PLY_RISK_GROUP   from ply_risk_person
		 where  `POLICY_NO` = #{policyNo,jdbcType=VARCHAR} and name =#{insuredName,jdbcType=VARCHAR}
		 and certificate_no = #{certificateNo,jdbcType=VARCHAR} )
	</select>

	<!--	方案号-->
	<select id="getDistinctRiskGroupNo" parameterType="com.paic.ncbs.claim.model.dto.ocas.OcasDTO"
			resultType="java.lang.String">
		select `risk_Group_No` from ply_risk_group where `ID_PLY_RISK_GROUP` in
		(select distinct  ID_PLY_RISK_GROUP   from ply_risk_person
		where  `POLICY_NO` = #{policyNo,jdbcType=VARCHAR} and name =#{insuredName,jdbcType=VARCHAR}
		and certificate_no = #{certificateNo,jdbcType=VARCHAR} ) limit 1
	</select>


	<!--查保单-险种-责任-->
	<select id="getPolicyPlanDuty" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO">
		select
	    distinct
		a.POLICY_NO policyNo,
		a.INSURANCE_BEGIN_DATE insuranceBeginDate,
		a.INSURANCE_END_DATE insuranceEndDate,
		a.TOTAL_INSURED_AMOUNT policyAmount,
		b.PLAN_CODE planCode,
		(select marketproduct_name from marketproduct_info m where m.marketproduct_code=a.product_code  order by CREATED_DATE desc  limit 0,1)
		productName,
		(select VALUE_CHINESE_ABBR_NAME from acss_parameter t where t.COLLECTION_CODE='BZ000' and a.AMOUNT_CURRENCY_CODE
		= t.VALUE_CODE limit 0,1) currency,
		e.name applicantName,
		d.name insuredName,
		(select b1.PLAN_CHINESE_NAME from plan_info b1 where b1.PLAN_CODE=b.PLAN_CODE and b1.STATUS='1') planName,
		c.DUTY_CODE dutyCode,
		(select c1.DUTY_CHINESE_NAME from duty_info c1 where c1.duty_CODE=c.duty_CODE and c1.STATUS='1') dutyName,
		(IFNULL(c.INSURED_AMOUNT, 0)*IFNULL(f.APPLY_NUM, 0)) dutyAmount,
		f.RISK_GROUP_TYPE riskGroupType,
		f.RISK_GROUP_NO riskGroupNo,
		f.RISK_GROUP_NAME riskGroupName,
		c.ID_PLY_DUTY idPlyDuty,
		d.ID_RISK_CLASS idRiskClass
		from ply_base_info a,ply_plan b ,ply_duty c,ply_risk_person d,ply_applicant_info e,ply_risk_group f
		where a.POLICY_NO=b.POLICY_NO
		and a.POLICY_NO=d.POLICY_NO
		and a.POLICY_NO=e.POLICY_NO
		and a.POLICY_NO=f.POLICY_NO
		and b.ID_PLY_PLAN=c.ID_PLY_PLAN
		and d.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and b.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and a.POLICY_NO in
		<foreach collection="policyNoList" item="item" index="index" separator="," open="(" close=")">
			#{item.policyNo,jdbcType=VARCHAR}
		</foreach>
		<if test="insuredName != null">
			and d.NAME = #{insuredName,jdbcType=VARCHAR}
		</if>
        <if test="certificateNo != null">
            and d.CERTIFICATE_NO=#{certificateNo,jdbcType=VARCHAR}
        </if>
	</select>

	<select id="getPlyRiskDutyRelation" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO">
		select INSURANCE_BEGIN_DATE  dutyInsuranceBeginDate,
		INSURANCE_END_DATE  dutyInsuranceEndDate
		from ply_risk_duty_relation
		where ID_DUTY=#{idPlyDuty}
		<if test="idRiskClass != null">
			and ID_RISK_CLASS = #{idRiskClass,jdbcType=VARCHAR}
		</if>
		order by created_date desc
		limit 1
	</select>
	<!--查被保人列表-->
	<select id="getReportInsuredList" parameterType="com.paic.ncbs.claim.model.vo.ocas.OcasReportQueryVO"
			resultType="com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO">
		select distinct
# 			a.id_ply_risk_person    idPlyRiskPerson,
			a.certificate_no        certificateNo,
			a.certificate_type      certificateType,
			a.name                  insuredName,
			a.personnel_attribute   personnelAttribute,
			a.birthday   			birthday,
# 		    a.policy_no             policyNo,
		    a.mobile_telephone      mobileTelephone,
# 		    (select PROFESSION_CHN_ABBR_NAME from clms_profession_define t where t.PROFESSION_CODE=a.profession_code ) profession
			(select value_chinese_name from acss_parameter t where collection_code ='ZK_ZYLX2022C' and t.value_code = a.profession_code  limit 0,1) profession
			from ply_risk_person a, ply_applicant_info b,ply_base_info c
		where a.policy_no = b.policy_no and a.policy_no = c.policy_no
# 		  and a.ID_RISK_CLASS in ( select distinct ID_RISK_CLASS from ply_risk_duty_relation d where d.policy_no = a.policy_no)
		<if test="certificateNo != null and certificateNo != '' ">
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_no = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="insuredName != null and insuredName != '' " >
			and a.name = #{insuredName,jdbcType=VARCHAR}
		</if>
		<if test="applicantName != null and applicantName != '' ">
			and b.name = #{applicantName,jdbcType=VARCHAR}
		</if>
		<if test="departmentCodes != null and departmentCodes != '' ">
			and c.department_code in
			<foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

	<!--查被保人名下保单列表-->
	<select id="getReportPolicyList" parameterType="com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO"
			resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO">
		select
		a.id_ply_risk_person   idPlyRiskPerson,
		a.policy_no            policyNo,
		a.certificate_no       certificateNo,
		a.certificate_type     certificateType,
		a.name                 insuredName,
		b.department_code      departmentCode,
		(select DEPARTMENT_ABBR_NAME from department_define d where d.department_code=b.department_code limit 0,1) departmentName,
		(SELECT CHANNEL_SOURCE_NAME FROM channel_source_define r,ply_sale s WHERE r.CHANNEL_SOURCE_CODE=s.CHANNEL_SOURCE_CODE AND s.POLICY_NO=b.policy_no LIMIT 0,1) channelSourceName,
		b.insurance_begin_date insuranceBeginDate,
		b.insurance_end_date   insuranceEndDate,
		b.status               status,
		b.product_code         productCode,
		c.name                 applicantName,
		a.PERSONNEL_ATTRIBUTE  personnelAttribute,
		a.mobile_telephone     mobileTelephone,
		a.certificate_type     certificateType,
		(select marketproduct_name from marketproduct_info m where m.marketproduct_code=b.product_code  order by CREATED_DATE desc limit 0,1) productName,
	    prg.RISK_GROUP_NAME riskGroupName
		from ply_risk_person a, ply_base_info b ,ply_applicant_info c, ply_risk_group prg
		where a.policy_no = b.policy_no
		and b.policy_no = c.policy_no
		and prg .id_ply_risk_group = a.id_ply_risk_group
		<if test="departmentCodes != null and departmentCodes != ''">
			and b.department_code in
			<foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="certificateNo != null and certificateNo != '' ">
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="insuredName != null and insuredName != '' ">
			and a.name = #{insuredName,jdbcType=VARCHAR}
		</if>
		<if test="certificateType != null and certificateType != '' ">
			and a.certificate_type = #{certificateType,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_no = #{policyNo,jdbcType=VARCHAR}
		</if>
		order by b.insurance_end_date desc
	</select>

	<select id="getInsuredBaseInfo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO">
		select
			a.certificate_no       certificateNo,
			a.certificate_type     certificateType,
			a.name                 insuredName,
			a.age                  age,
			a.CERTIFICATE_TYPE     certificateType,
			a.personnel_attribute  personnelAttribute,
		    a.SEX_CODE             sexCode,
			a.client_no  		   clientNo,
			a.mobile_telephone     mobileTelephone,
		    a.BIRTHDAY 			   birthday,
		    a.IS_SOCIA_SECURITY    isSociaSecurity,
		    a.POLICY_NO            policyNo,
			a.personnel_nature     personnelNature
		from ply_risk_person a
		where a.id_ply_risk_person = #{idPlyRiskPerson,jdbcType=VARCHAR}
	</select>


	<select id="getPersionRiskId" resultType="java.lang.String"  >
		select id_ply_risk_person   clientNo from ply_risk_person a where 1=1
		<if test="certificateNo != null and certificateNo != '' " >
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="certificateType != null and certificateType != '' ">
			and a.certificate_type = #{certificateType,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_No = #{policyNo,jdbcType=VARCHAR}
		</if>
        limit 1
	</select>
	<select id="getInsuredListPage" parameterType="com.paic.ncbs.claim.model.dto.ocas.OcasDTO"
			resultType="com.paic.ncbs.claim.model.dto.ocas.OcasDTO">
		select t.*,d.profession_chn_name  professionChnName  from
		(select DISTINCT
		a.certificate_no       certificateNo,
		a.certificate_type     certificateType,
		a.name                 insuredName,
		a.birthday             birthday,
		a.personnel_attribute  personnelAttribute,
		a.PROFESSION_CLASS     professionClass,
		a.profession_code
		from ply_risk_person a, ply_base_info b ,ply_applicant_info c
		where a.policy_no =c.policy_no
			  and a.policy_no = b.policy_no
		<if test="certificateNo != null and certificateNo != '' " >
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_no = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="departmentCodes != null and departmentCodes != ''">
			and b.department_code in
			<foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="insuredName != null and insuredName != ''  ">
			and a.name = #{insuredName,jdbcType=VARCHAR}
		</if>
		) t left join clms_profession_define d on t.profession_code =d.profession_code
	</select>

	<select id="policyCopyAndSendDetail" resultType="com.paic.ncbs.claim.model.dto.ahcs.PolicyCopySendDetail">
		select distinct p.`POLICY_NO`  ,p.PRODUCT_CODE,p.`DEPARTMENT_CODE` ,p2.name,p2.PROFESSION_CODE,p2.ADDRESS ,p2.MOBILE_TELEPHONE LINK_MAN_TELEPHONE , p.TOTAL_AGREE_PREMIUM  ,
			   p.TOTAL_ACTUAL_PREMIUM  ,p3.ACTUAL_DATE ,p.ACCEPT_INSURANCE_DATE  ,p.BUSINESS_TYPE ,p4.inner_coinsurance_mark  coinsurance_mark,
			   p.RENEWAL_TYPE ,p4.IS_FACULTATIVE_BUSINESS,p.contract_pact_code ,
		 (select JOINT_INSURANCE_BUSINESS  from ply_coinsurance_info pci where POLICY_NO = #{policyNo} limit 1) coinsuranceMark,
		 (select `CHANNEL_SOURCE_NAME` from channel_source_define csd  where CHANNEL_SOURCE_CODE  =
		 (select CHANNEL_SOURCE_CODE  from  ply_sale where `POLICY_NO`  = #{policyNo} ) ) CHANNEL_SOURCE_NAME,
		 (select  EMPLOYEE_NAME from  ply_employee where `POLICY_NO` = #{policyNo} limit 1) SALE_AGENT_NAME,
		 (select  FACULTATIVE_TYPE from  ply_reinsurance  where `POLICY_NO` = #{policyNo} limit 1) FACULTATIVE_TYPE,
		 (select
			 GROUP_CONCAT(distinct combined_name separator ',')
		 from
			 ply_health_services
		 where
				 id_ply_risk_group in (
				 select
					 id_ply_risk_group
				 from
					 ply_risk_group
				 where
					 policy_no = #{policyNo} ) )  combined_name,
		      p.ENDORSE_NO ,p6.SCENE_LIST STATUS ,p6.EFFECTIVE_DATE ,
	(select marketproduct_name from marketproduct_info m where m.marketproduct_code=p.product_code  order by CREATED_DATE desc limit 0,1) product_Name
		from  ply_base_info p left  join ply_applicant_info p2  on  p.`POLICY_NO`  = p2.`POLICY_NO`
							  left  join (select *  from ply_pay_info where policy_no =#{policyNo} and status = '01' order by term_no desc limit 1 )  p3 on  p.`POLICY_NO`  = p3.`POLICY_NO`
		                      left  join  ply_extend p4  on p4.`POLICY_NO`  = p.`POLICY_NO`
		                      left  join  (select EFFECTIVE_DATE,SCENE_LIST ,policy_No from edr_apply_base_info
							  where   SCENE_LIST in ('00006','00017') and policy_No =#{policyNo} and EFFECTIVE_DATE is not null order by CREATED_DATE desc limit 1) p6
			                  on p.`POLICY_NO`  = p6.`POLICY_NO`
		where   p.`POLICY_NO`  =#{policyNo} limit 1
	</select>



	<!--受益人信息-->
	<select id="benefitInfos" resultType="com.paic.ncbs.claim.model.dto.settle.BeneficaryDTO">
		select  *  from  ply_beneficiary_info   where `POLICY_NO`  = #{policyNo}
		and ID_PLY_RISK_PERSON = (select   `ID_PLY_RISK_PERSON` from  ply_risk_person
		where `CERTIFICATE_NO` = #{certificateNo} and `NAME` = #{insuredName} and `POLICY_NO` = #{policyNo} limit 1)
	</select>

	<!--查询产品类型-->
	<select id="getPlyBaseInfo" resultType="java.util.Map">
		select
		       product_class productClass,
		       PRODUCT_CODE productCode,
			   department_code departmentCode,
		       BUSINESS_TYPE businessType
		from ply_base_info where POLICY_NO = #{policyNo}
	</select>

	<!--查询产品类型-->
	<select id="getPlyPlanInfo" resultType="java.lang.String">
		select
		       product_class planClass
		from ply_plan where POLICY_NO = #{policyNo} and PLAN_CODE = #{planCode}
		limit 1
	</select>

	<!--查询险种类型-->
	<select id="getPolicyPlan" resultType="java.util.Map">
		select IS_MAIN isMain  from ply_plan where PLAN_CODE = #{planCode}
	</select>

	<!--查真实被保人列表-->
	<select id="getRiskPersonnalList" parameterType="java.util.List"
			resultType="com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO">
		select distinct
		a.id_ply_risk_person    idPlyRiskPerson,
		a.certificate_no        certificateNo,
		a.certificate_type      certificateType,
		a.name                  insuredName,
		a.personnel_attribute   personnelAttribute,
		a.birthday   			birthday,
		a.policy_no             policyNo,
		(select b.RISK_GROUP_NAME  from ply_risk_group b where b.ID_PLY_RISK_GROUP =a.ID_PLY_RISK_GROUP) groupName
		from PLY_RISK_PERSON a
		where a.policy_no  in
		<foreach collection="policyList" item="policy" open="(" close=")" separator=",">
			#{policy,jdbcType=VARCHAR}
		</foreach>
		and a.personnel_attribute in ('100','101')

	</select>


	<!--查保单-险种-责任-->
	<select id="getPolicyCopyDuty" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO">
		select
	    distinct
		a.POLICY_NO policyNo,
		a.INSURANCE_BEGIN_DATE insuranceBeginDate,
		a.INSURANCE_END_DATE insuranceEndDate,
		a.TOTAL_INSURED_AMOUNT policyAmount,
		b.PLAN_CODE planCode,
		(select marketproduct_name from marketproduct_info m where m.marketproduct_code=a.product_code  order by CREATED_DATE desc  limit 0,1)
		productName,
		(select VALUE_CHINESE_ABBR_NAME from acss_parameter t where t.COLLECTION_CODE='BZ000' and a.AMOUNT_CURRENCY_CODE
		= t.VALUE_CODE limit 0,1) currency,
		e.name applicantName,
		(select b1.PLAN_CHINESE_NAME from plan_info b1 where b1.PLAN_CODE=b.PLAN_CODE and b1.STATUS='1') planName,
		c.DUTY_CODE dutyCode,
		(select c1.DUTY_CHINESE_NAME from duty_info c1 where c1.duty_CODE=c.duty_CODE and c1.STATUS='1' ) dutyName,
		(IFNULL(c.INSURED_AMOUNT, 0)*IFNULL(f.APPLY_NUM, 0)) dutyAmount ,
		if((select pd.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
			where pi.POLICY_NO =#{policyNo} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
			  and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=c.duty_code limit 1) =
		'1',TRUE,FALSE) isShareAmount,
		(select pd.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
		 where pi.POLICY_NO =#{policyNo} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
		   and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=c.duty_code limit 1) shareDutyGroup,
		f.RISK_GROUP_NO riskGroupNo,
		f.RISK_GROUP_NAME riskGroupName
		from ply_base_info a,ply_plan b ,ply_duty c,ply_applicant_info e,ply_risk_group f,ply_risk_person d
		where a.POLICY_NO=b.POLICY_NO
		and a.POLICY_NO=e.POLICY_NO
		and a.POLICY_NO=f.POLICY_NO
		and b.ID_PLY_PLAN=c.ID_PLY_PLAN
		and b.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and d.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and a.POLICY_NO =#{policyNo}
		and d.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
		and d.NAME = #{insuredName,jdbcType=VARCHAR}
	</select>

	<select id="getPolicyNoFromBase" resultType="java.lang.String" parameterType="java.lang.String">
		select POLICY_NO
		from ply_base_info
		where POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getPolicyStopDate" resultType="java.util.Date" parameterType="java.lang.String">
		select EFFECTIVE_DATE
		from edr_apply_base_info
		where POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		and SCENE_LIST = '20023'
		and EFFECTIVE_DATE is not null
		order by CREATED_DATE desc
		limit 1
	</select>

	<select id="getRiskPersonNo" resultType="java.lang.String" parameterType="java.lang.String">
		select RISK_PERSON_NO
		from PLY_RISK_PERSON a
		where a.policy_no = #{policyNo,jdbcType=VARCHAR}
		and a.name = #{insuredName,jdbcType=VARCHAR}
		and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getRiskPersonCode" resultType="java.lang.String" parameterType="java.lang.String">
		select PERSONNEL_CODE
		from PLY_RISK_PERSON a
		where a.policy_no = #{policyNo,jdbcType=VARCHAR}
		and a.name = #{insuredName,jdbcType=VARCHAR}
		and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getPolicyActualPremiumList" resultType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
		select
			ifnull(sum(AGREE_PREMIUM),0)  totalAgreePremium,
			ifnull(sum(ACTUAL_PREMIUM),0) totalActualPremium,
			POLICY_NO                     policyNo
		from ply_pay_info
		where STATUS = '01'
		and POLICY_NO in
		(select distinct POLICY_NO
		from clms_policy_info b
		where b.REPORT_NO=#{reportNo,jdbcType=VARCHAR})
		group by POLICY_NO
	</select>

	<select id="getPolicyActualPremium" resultType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
		select ifnull(sum(AGREE_PREMIUM),0) totalAgreePremium,
		ifnull(sum(ACTUAL_PREMIUM),0) totalActualPremium
		from ply_pay_info
		where STATUS = '01'
		and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
	</select>

	<!--保单代理人信息表-->
	<select id="getPlyAgentByPolicyNo" resultType="java.util.Map">
		select
			AGENT_CODE agentCode,
			AGENT_AGREEMENT_NO agentAgreementNo,
			SUPPLEMENT_AGREEMENT_NO supplementAgreementNo
		from ply_agent where POLICY_NO = #{policyNo}
	</select>

	<!--保单销售信息表-->
	<select id="getPlySaleByPolicyNo" resultType="java.util.Map">
		select
			BUSINESS_SOURCE_CODE businessSourceCode,
		    BUSINESS_SOURCE_DETAIL_CODE businessSourceDetailCode,
		    CHANNEL_SOURCE_CODE channelSourceCode,
    		CHANNEL_SOURCE_DETAIL_CODE channelSourceDetailCode
		from ply_sale where POLICY_NO = #{policyNo}
	</select>

	<!--查保单-被保人-投保人-->
	<select id="getPolicyInfoForPay" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPayDTO">
		select
		distinct
		a.POLICY_NO policyNo,
		a.DEPARTMENT_CODE departmentCode,
		a.INSURANCE_BEGIN_DATE insuranceBeginDate,
		a.INSURANCE_END_DATE insuranceEndDate,
		e.CLIENT_NO applicantClientNo,
		e.name applicantName,
		d.CLIENT_NO insuredClientNo,
		(select e.property_value
		from ply_property e where e.policy_no=a.POLICY_NO and e.property_code='profitCenter') as profitCenterCode
		from ply_base_info a,ply_applicant_info e,ply_risk_group f,ply_risk_person d
		where a.POLICY_NO=e.POLICY_NO
		and a.POLICY_NO=f.POLICY_NO
		and d.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and a.POLICY_NO =#{policyNo}
		and d.CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
		<!--<if test="certificateNo != null and certificateNo != '' ">
			and d.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
		</if>
		and d.NAME = #{insuredName,jdbcType=VARCHAR}-->
	</select>

	<!-- 客户号 -->
	<select id="getClientInfoByFiveInfo" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity"
			resultType="java.lang.String" >
		select
			client_no as clientNo
		from
			ply_client_info
		<where>
			<choose>
				<when test="certificateNo != null">
					and CERTIFICATE_NO = #{certificateNo}
				</when>
				<otherwise>
					and CERTIFICATE_NO is null
				</otherwise>
			</choose>
			<choose>
				<when test="name != null">
					and NAME = #{name}
				</when>
				<otherwise>
					and NAME is null
				</otherwise>
			</choose>
			<choose>
				<when test="certificateType != null">
					and CERTIFICATE_TYPE = #{certificateType}
				</when>
				<otherwise>
					and CERTIFICATE_TYPE is null
				</otherwise>
			</choose>
			<choose>
				<when test="birthday != null">
					and BIRTHDAY = #{birthday}
				</when>
				<otherwise>
					and BIRTHDAY is null
				</otherwise>
			</choose>
			<choose>
				<when test="sexCode != null">
					and SEX_CODE = #{sexCode}
				</when>
				<otherwise>
					and SEX_CODE is null
				</otherwise>
			</choose>
		</where>
		limit 1
	</select>

	<!-- 客户号 -->
	<select id="getClientNo" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity"
			resultType="java.lang.String" >
		select
		client_no as clientNo
		from
		ply_client_info
		where
		CERTIFICATE_NO = #{certificateNo}
		and NAME = #{name}
		and CERTIFICATE_TYPE = #{certificateType}
		limit 1
	</select>

	<!-- 客户号 -->
	<select id="getApplicantInfo" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasApplicantInfoDTO"
			parameterType="java.lang.String" >
		select
			POLICY_NO as policyNo,
			NAME as name,
			CERTIFICATE_NO as certificateNo,
			CERTIFICATE_TYPE as certificateType
		from
			ply_applicant_info
		where
			POLICY_NO =#{policyNo}
		limit 1
	</select>

	<insert id="saveClientNo" parameterType="com.paic.ncbs.claim.model.dto.customer.ClientManagementDTO">
		INSERT INTO
		ply_client_info (
		<trim suffixOverrides=",">
			<if test="clientNo != null and clientNo != ''">
				client_no,
			</if>
			<if test="certificateNo != null and certificateNo != ''">
				CERTIFICATE_NO,
			</if>
			<if test="certificateType != null and certificateType != ''">
				CERTIFICATE_TYPE,
			</if>
			<if test="birthday != null">
				BIRTHDAY,
			</if>
			<if test="sexCode != null and sexCode != ''">
				SEX_CODE,
			</if>
			<if test="name != null and name != ''">
				NAME,
			</if>
			<if test="newClientNo != null and newClientNo != ''">
				NEW_CLIENT_NO,
			</if>
		</trim>
		) values (
		<trim suffixOverrides=",">
			<if test="clientNo != null and clientNo != ''">
				#{clientNo},
			</if>
			<if test="certificateNo != null and certificateNo != ''">
				#{certificateNo},
			</if>
			<if test="certificateType != null and certificateType != ''">
				#{certificateType},
			</if>
			<if test="birthday != null">
				#{birthday},
			</if>
			<if test="sexCode != null and sexCode != ''">
				#{sexCode},
			</if>
			<if test="name != null and name != ''">
				#{name},
			</if>
			<if test="newClientNo != null and newClientNo != ''">
				#{newClientNo},
			</if>
		</trim>
		)
	</insert>

	<select id="getPolicyInfoByPolicyNo" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO">
		select
		INSURANCE_BEGIN_DATE insuranceBeginDate,
		INSURANCE_END_DATE insuranceEndDate
		from ply_base_info
		where  POLICY_NO = #{policyNo}
	</select>

	<select id="getRiskGroupByPolicyNo" resultType="java.util.Map">
		SELECT  product_package_type productPackageType,
				risk_group_name riskGroupName
		FROM ply_risk_group
		WHERE id_ply_risk_group = (
			SELECT id_ply_risk_group
			FROM ply_risk_person
			WHERE policy_no = #{policyNo}
			AND client_no = #{clientNo}
			LIMIT 1
		)
	</select>

	<select id="getTargetType" resultType="java.lang.String">
		select b.TARGET_TYPE
		from ply_base_info a,marketproduct_info b
		where b.MARKETPRODUCT_CODE = a.PRODUCT_CODE
		and b.VERSION =a.PRODUCT_VERSION
		and a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		order by b.CREATED_DATE desc limit 1
	</select>

	<select id="getPrudocutInfo" resultType="com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO">
		SELECT a.product_class, b.target_type
		FROM ply_base_info a, marketproduct_info b
		WHERE b.marketproduct_code = a.product_code
		AND b.version = a.product_version
		AND a.policy_no = #{policyNo}
		ORDER BY b.CREATED_DATE DESC
		LIMIT 1
	</select>

	<select id="getPropertyInfo" resultType="com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO"
			parameterType = "com.paic.ncbs.claim.model.dto.ocas.ProductInfoDTO">
		SELECT a.product_class, b.target_type
		FROM ply_base_info a, marketproduct_info b
		WHERE b.marketproduct_code = a.product_code
		AND b.version = a.product_version
		AND a.policy_no = #{policyNo}
		AND a.product_class in
		<foreach collection="classes" open="(" close=")"  item="item" separator=",">
			#{item,jdbcType=VARCHAR}
		</foreach>
		and b.target_type in
		<foreach collection="targetTypeList" open="(" close=")"  item="item" separator=",">
			#{item,jdbcType=VARCHAR}
		</foreach>
		ORDER BY b.CREATED_DATE DESC
		LIMIT 1
	</select>

	<!--查保单-险种-责任-->
	<select id="getPolicyCopyDutyList" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO"
			parameterType="com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO">
		select
			distinct
			a.POLICY_NO policyNo,
			a.INSURANCE_BEGIN_DATE insuranceBeginDate,
			a.INSURANCE_END_DATE insuranceEndDate,
			a.TOTAL_INSURED_AMOUNT policyAmount,
			b.PLAN_CODE planCode,
			(select marketproduct_name from marketproduct_info m where m.marketproduct_code=a.product_code  order by CREATED_DATE desc  limit 0,1)
				productName,
			(select VALUE_CHINESE_ABBR_NAME from acss_parameter t where t.COLLECTION_CODE='BZ000' and a.AMOUNT_CURRENCY_CODE
				= t.VALUE_CODE limit 0,1) currency,
			e.name applicantName,
			(select b1.PLAN_CHINESE_NAME from plan_info b1 where b1.PLAN_CODE=b.PLAN_CODE and b1.STATUS='1') planName,
			c.DUTY_CODE dutyCode,
			(select c1.DUTY_CHINESE_NAME from duty_info c1 where c1.duty_CODE=c.duty_CODE and c1.STATUS='1' ) dutyName,
			(IFNULL(c.INSURED_AMOUNT, 0)*IFNULL(f.APPLY_NUM, 0)) dutyAmount ,

			if((select pd.IS_DUTY_SHARED_AMOUNT from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
				where pi.POLICY_NO =#{policyNo} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
				  and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=c.duty_code limit 1) = '1',TRUE,FALSE) IS_SHARE_AMOUNT,
			(select pd.DUTY_SHARED_AMOUNT_MERGE from CLMS_policy_duty pd,clms_policy_plan pp,clms_policy_info pi
			 where pi.POLICY_NO =#{policyNo} and pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
			   and pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN and pd.DUTY_CODE=c.duty_code limit 1) SHARE_DUTY_GROUP,
		    d.CLIENT_NO clientNo

		from ply_base_info a,ply_plan b ,ply_duty c,ply_applicant_info e,ply_risk_group f,ply_risk_person d
		where a.POLICY_NO=b.POLICY_NO
		  and a.POLICY_NO=e.POLICY_NO
		  and a.POLICY_NO=f.POLICY_NO
		  and b.ID_PLY_PLAN=c.ID_PLY_PLAN
		  and b.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		  and d.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		  and a.POLICY_NO =#{policyNo}
		  and d.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
		  and d.NAME = #{insuredName,jdbcType=VARCHAR}
		  and exists(select 1 from ply_risk_propsub_group g,ply_risk_property h
		                      where g.ID_PLY_RISK_PROPERTY = h.ID_PLY_RISK_PROPERTY
		                        and h.ID_PLY_RISK_GROUP = d.ID_PLY_RISK_GROUP
		                      	and g.ID_PLY_RISK_PROPSUB_GROUP = #{idPlyRiskProperty,jdbcType=VARCHAR})
	</select>

	<select id="getRiskPropertyList" parameterType="com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO"
			resultType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO">
		select c.ID_PLY_RISK_PROPSUB_GROUP idPlyRiskProperty,
		       c.certificate_type certificateType,
		       c.CERTIFICATE_NO certificateNo,
		       c.NAME name,
		       c.age age,
		       c.sex sex,
		       b.RISK_DETAIL riskDetail,
			   a.ID_PLY_RISK_GROUP idPlyRiskGroup,
		       c.POLICY_NO policyNo,
             (select d.RISK_GROUP_NAME from ply_risk_group d
              where d.ID_PLY_RISK_GROUP = a.ID_PLY_RISK_GROUP) riskGroupName
		from ply_risk_person a ,ply_risk_property b ,ply_risk_propsub_group c
		where a.ID_PLY_RISK_GROUP = b.ID_PLY_RISK_GROUP
		and c.ID_PLY_RISK_PROPERTY = b.ID_PLY_RISK_PROPERTY
		and a.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
		and a.NAME = #{insuredName,jdbcType=VARCHAR}
		and a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		and a.CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR}
		<if test="idPlyRiskPropertyList!= null">
			and c.ID_PLY_RISK_PROPSUB_GROUP in (
			<foreach collection="idPlyRiskPropertyList" item="item" separator=",">
				#{item,jdbcType=VARCHAR}
			</foreach>
			)
		</if>
	</select>

	<!--查保单-险种-责任-->
	<select id="getPolicyPlanDutyList" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO">
		select
		distinct
		a.POLICY_NO policyNo,
		a.INSURANCE_BEGIN_DATE insuranceBeginDate,
		a.INSURANCE_END_DATE insuranceEndDate,
		a.TOTAL_INSURED_AMOUNT policyAmount,
		b.PLAN_CODE planCode,
		(select marketproduct_name from marketproduct_info m where m.marketproduct_code=a.product_code  order by CREATED_DATE desc limit 0,1)
		productName,
		(select VALUE_CHINESE_ABBR_NAME from acss_parameter t where t.COLLECTION_CODE='BZ000' and a.AMOUNT_CURRENCY_CODE
		= t.VALUE_CODE limit 0,1) currency,
		e.name applicantName,
		d.name insuredName,
		(select b1.PLAN_CHINESE_NAME from plan_info b1 where b1.PLAN_CODE=b.PLAN_CODE and b1.STATUS='1') planName,
		c.DUTY_CODE dutyCode,
		(select c1.DUTY_CHINESE_NAME from duty_info c1 where c1.duty_CODE=c.duty_CODE and c1.STATUS='1') dutyName,
		(IFNULL(c.INSURED_AMOUNT, 0)*IFNULL(f.APPLY_NUM, 0)) dutyAmount,
		f.RISK_GROUP_TYPE riskGroupType,
		f.RISK_GROUP_NO riskGroupNo,
		f.RISK_GROUP_NAME riskGroupName,c.ID_PLY_DUTY idPlyDuty,d.ID_RISK_CLASS idRiskClass
		from ply_base_info a,ply_plan b ,ply_duty c,ply_risk_person d,ply_applicant_info e,ply_risk_group f
		where a.POLICY_NO=b.POLICY_NO
		and a.POLICY_NO=d.POLICY_NO
		and a.POLICY_NO=e.POLICY_NO
		and a.POLICY_NO=f.POLICY_NO
		and b.ID_PLY_PLAN=c.ID_PLY_PLAN
		and d.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and b.ID_PLY_RISK_GROUP = f.ID_PLY_RISK_GROUP
		and d.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
		and a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		and d.NAME = #{insuredName,jdbcType=VARCHAR}
		and d.CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR}
		and exists(select 1 from ply_risk_propsub_group g,ply_risk_property h
			 where g.ID_PLY_RISK_PROPERTY = h.ID_PLY_RISK_PROPERTY
			   and h.ID_PLY_RISK_GROUP = d.ID_PLY_RISK_GROUP
			   and g.ID_PLY_RISK_PROPSUB_GROUP = #{idPlyRiskProperty,jdbcType=VARCHAR})
	</select>

	<select id="getPolicyApplyMode" resultType="java.lang.String" parameterType="java.lang.String">
		select apply_mode applyMode
		from ply_base_info
		where POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getPolicyDocumentId" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT
		c.document_id
		FROM
		ply_base_info b,
		ply_attachment a,
		document c
		WHERE
		a.policy_no = #{policyNo,jdbcType=VARCHAR}
		AND ( a.DOCUMENT_GROUP_TYPE = '02' OR a.DOCUMENT_GROUP_TYPE IS NULL )
		AND a.document_group_id IS NOT NULL
		AND b.id_ply_base_info = a.id_ply_base_info
		AND a.DOCUMENT_GROUP_ID = c.DOCUMENT_GROUP_ID
		AND a.file_type = 'T20'
		limit 1
	</select>

	<select id="getDocumentIdByPolicyNo" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT
		c.document_id
		FROM
		ply_attachment a,
		document c
		WHERE
		a.policy_no = (select APPLY_POLICY_NO from aply_base_info
						where POLICY_NO = #{policyNo,jdbcType=VARCHAR})
		AND a.DOCUMENT_GROUP_ID = c.DOCUMENT_GROUP_ID
		AND c.DOCUMENT_TYPE = 'P003'
		limit 1
	</select>

	<select id="getRiskPersonnalListByName" parameterType="java.util.List"
			resultType="com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO">
		select distinct
		a.id_ply_risk_person    idPlyRiskPerson,
		a.certificate_no        certificateNo,
		a.certificate_type      certificateType,
		a.name                  insuredName,
		a.personnel_attribute   personnelAttribute,
		a.birthday   			birthday,
		a.policy_no             policyNo,
		(select b.RISK_GROUP_NAME  from ply_risk_group b where b.ID_PLY_RISK_GROUP =a.ID_PLY_RISK_GROUP) groupName
		from PLY_RISK_PERSON a
		where a.policy_no  in
		<foreach collection="policyList" item="policy" open="(" close=")" separator=",">
			#{policy,jdbcType=VARCHAR}
		</foreach>
        <if test="name != null and name != ''">
            and a.name =#{name,jdbcType=VARCHAR}
        </if>
		and a.personnel_attribute in ('200','020')
	</select>

	<select id="getRiskPropertyParamByPolicyNo" parameterType="java.lang.String"
			resultType="com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyParamDTO">
		select distinct
		a.COMBINED_PRODUCT_CODE productCode,
		a.PRODUCT_PACKAGE_TYPE productPackageType,
		a.PRODUCT_CLASS productClass,
		a.RISK_GROUP_TYPE riskGroupType,
		a.RISK_GROUP_NAME riskGroupName,
		a.risk_Group_No riskGroupNo
		from ply_risk_group a
		where a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
	</select>


	<select id="getPlanClassByInsured"  resultType="java.lang.String">
		select distinct b. product_class planClass
		from ply_base_info a,ply_plan b ,ply_risk_person d
		where a.POLICY_NO=b.POLICY_NO
		and a.POLICY_NO=d.POLICY_NO
		and d.CERTIFICATE_NO=#{certificateNo,jdbcType=VARCHAR}
		and a.POLICY_NO in
		<foreach collection="policyNoList" item="item" index="index" separator="," open="(" close=")">
			#{item,jdbcType=VARCHAR}
		</foreach>
		<if test="insuredName != null">
			and d.NAME = #{insuredName,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="checkNonTaxExemptDuty" resultType="java.lang.Boolean">·
		SELECT
			CASE
			WHEN EXISTS (
			SELECT 1
			FROM ply_duty d
			JOIN PLY_PLAN p ON d.ID_PLY_PLAN = p.ID_PLY_PLAN
			WHERE d.duty_code = #{dutyCode,jdbcType=VARCHAR}
			AND p.plan_code = #{planCode,jdbcType=VARCHAR}
			AND d.policy_No = #{policyNo,jdbcType=VARCHAR}
			AND (COALESCE(d.TOTAL_VALUE_ADDED_TAX, 0) &lt;= 0 or d.TOTAL_VALUE_ADDED_TAX IS null)
			) THEN 'true'
			ELSE 'false'
		END AS result;
	</select>
	
</mapper>